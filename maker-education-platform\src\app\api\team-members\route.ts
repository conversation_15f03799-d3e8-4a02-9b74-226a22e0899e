import { NextRequest, NextResponse } from 'next/server'
import { safeDbOperation } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const includeInactive = searchParams.get('includeInactive') === 'true'

  const where = includeInactive ? {} : { isActive: true }

  const teamMembers = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')
    return await prisma.teamMember.findMany({
      where,
      orderBy: [
        { displayOrder: 'asc' },
        { joinDate: 'desc' }
      ],
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })
  }, [])

  // 解析JSON字符串为数组
  const processedMembers = teamMembers.map(member => ({
    ...member,
    skills: member.skills ? JSON.parse(member.skills) : []
  }))

  return NextResponse.json(processedMembers)
}

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const {
      userId,
      name,
      position,
      department,
      avatar,
      bio,
      skills,
      displayOrder = 0
    } = await request.json()

    // 验证必填字段
    if (!name || !position) {
      return NextResponse.json(
        { error: '姓名和职位为必填项' },
        { status: 400 }
      )
    }

    const teamMember = await safeDbOperation(async () => {
      const { prisma } = await import('@/lib/prisma')
      return await prisma.teamMember.create({
        data: {
          userId,
          name,
          position,
          department,
          avatar,
          bio,
          skills: JSON.stringify(skills || []),
          displayOrder,
          isActive: true,
          joinDate: new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            }
          }
        }
      })
    }, null)

    if (!teamMember) {
      return NextResponse.json(
        { error: '创建团队成员失败' },
        { status: 500 }
      )
    }

    // 解析JSON字符串为数组
    const processedMember = {
      ...teamMember,
      skills: teamMember.skills ? JSON.parse(teamMember.skills) : []
    }

    return NextResponse.json(processedMember, { status: 201 })
  } catch (error) {
    console.error('Failed to create team member:', error)
    return NextResponse.json(
      { error: '创建团队成员失败' },
      { status: 500 }
    )
  }
})
