'use client'

import { useState } from 'react'
import { Share2, Co<PERSON>, Check, Image as ImageIcon, Link as LinkIcon, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ArticlePoster } from './article-poster'

interface ArticleShareProps {
  title: string
  excerpt: string
  author: string
  publishDate: string
  slug: string
  readTime?: string
  category?: string
}

export function ArticleShare({
  title,
  excerpt,
  author,
  publishDate,
  slug,
  readTime,
  category
}: ArticleShareProps) {
  const [showShareModal, setShowShareModal] = useState(false)
  const [shareType, setShareType] = useState<'link' | 'poster'>('link')
  const [copied, setCopied] = useState(false)

  const articleUrl = `${window.location.origin}/blog/${slug}`

  const shareOptions = [
    {
      name: '微信',
      icon: '💬',
      action: () => {
        copyToClipboard()
      }
    },
    {
      name: '微博',
      icon: '🐦',
      action: () => {
        const url = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(articleUrl)}&title=${encodeURIComponent(title)}`
        window.open(url, '_blank')
      }
    },
    {
      name: 'QQ',
      icon: '🐧',
      action: () => {
        const url = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(articleUrl)}&title=${encodeURIComponent(title)}`
        window.open(url, '_blank')
      }
    },
    {
      name: 'Twitter',
      icon: '🐦',
      action: () => {
        const url = `https://twitter.com/intent/tweet?url=${encodeURIComponent(articleUrl)}&text=${encodeURIComponent(title)}`
        window.open(url, '_blank')
      }
    }
  ]

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(articleUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setShowShareModal(true)}
        className="flex items-center space-x-2"
      >
        <Share2 className="w-4 h-4" />
        <span>分享文章</span>
      </Button>

      {showShareModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900">分享文章</h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* 分享类型选择 */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex space-x-4">
                <button
                  onClick={() => setShareType('link')}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                    shareType === 'link'
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <LinkIcon className="w-4 h-4" />
                  <span>分享链接</span>
                </button>
                <button
                  onClick={() => setShareType('poster')}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                    shareType === 'poster'
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <ImageIcon className="w-4 h-4" />
                  <span>生成海报</span>
                </button>
              </div>
            </div>

            {/* 分享内容 */}
            <div className="p-6">
              {shareType === 'link' ? (
                <div className="space-y-6">
                  {/* 链接分享 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      文章链接
                    </label>
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border">
                      <input
                        type="text"
                        value={articleUrl}
                        readOnly
                        className="flex-1 bg-transparent text-sm text-gray-600 outline-none"
                      />
                      <button
                        onClick={copyToClipboard}
                        className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                      >
                        {copied ? (
                          <Check className="w-4 h-4 text-green-500" />
                        ) : (
                          <Copy className="w-4 h-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* 社交平台分享 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      分享到社交平台
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {shareOptions.map((option, index) => (
                        <button
                          key={index}
                          onClick={option.action}
                          className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                        >
                          <span className="text-xl">{option.icon}</span>
                          <span className="text-sm font-medium text-gray-700">
                            {option.name}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <ArticlePoster
                    title={title}
                    excerpt={excerpt}
                    author={author}
                    publishDate={publishDate}
                    readTime={readTime}
                    category={category}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  )
}
