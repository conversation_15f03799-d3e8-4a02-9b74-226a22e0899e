'use client'

import { useProjectInteraction } from '@/hooks/useProjectInteraction'
import { LikeButton } from '@/components/ui/like-button'
import { ShareButton } from '@/components/ui/share-button'
import { Eye, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProjectInteractionProps {
  projectId: string
  projectTitle: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  layout?: 'horizontal' | 'vertical'
}

export function ProjectInteraction({
  projectId,
  projectTitle,
  className,
  size = 'md',
  layout = 'horizontal'
}: ProjectInteractionProps) {
  const {
    liked,
    likeCount,
    viewCount,
    shareCount,
    isLoading,
    error,
    toggleLike,
    share,
    clearError,
    isLoggedIn
  } = useProjectInteraction(projectId)

  if (isLoading) {
    return (
      <div className={cn(
        'flex items-center gap-4',
        layout === 'vertical' && 'flex-col',
        className
      )}>
        <div className="animate-pulse flex items-center gap-2">
          <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
          <div className="w-8 h-4 bg-gray-200 rounded"></div>
        </div>
        <div className="animate-pulse flex items-center gap-2">
          <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
          <div className="w-8 h-4 bg-gray-200 rounded"></div>
        </div>
        <div className="animate-pulse flex items-center gap-2">
          <div className="w-6 h-6 bg-gray-200 rounded"></div>
          <div className="w-8 h-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      'flex items-center gap-4',
      layout === 'vertical' && 'flex-col items-start',
      className
    )}>
      {/* 错误提示 */}
      {error && (
        <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 px-3 py-2 rounded-lg">
          <AlertCircle className="w-4 h-4" />
          <span>{error}</span>
          <button
            onClick={clearError}
            className="ml-2 text-red-400 hover:text-red-600"
          >
            ×
          </button>
        </div>
      )}

      {/* 点赞按钮 */}
      <LikeButton
        liked={liked}
        likeCount={likeCount}
        onToggle={toggleLike}
        disabled={!isLoggedIn}
        size={size}
      />

      {/* 分享按钮 */}
      <ShareButton
        projectId={projectId}
        projectTitle={projectTitle}
        shareCount={shareCount}
        onShare={share}
        size={size}
      />

      {/* 浏览数 */}
      <div className={cn(
        'flex items-center gap-2 text-gray-500',
        size === 'sm' && 'text-sm',
        size === 'lg' && 'text-lg'
      )}>
        <Eye className={cn(
          'text-gray-400',
          size === 'sm' && 'w-4 h-4',
          size === 'md' && 'w-5 h-5',
          size === 'lg' && 'w-6 h-6'
        )} />
        <span>{viewCount}</span>
      </div>

      {/* 未登录提示 */}
      {!isLoggedIn && (
        <div className="text-xs text-gray-400">
          <a href="/auth/signin" className="hover:text-blue-500 underline">
            登录后可点赞
          </a>
        </div>
      )}
    </div>
  )
}
