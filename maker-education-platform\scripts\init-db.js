const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function initializeDatabase() {
  try {
    console.log('🚀 开始初始化数据库...')

    // 检查是否已有管理员用户
    const existingAdmin = await prisma.user.findUnique({
      where: { username: 'admin' }
    })

    if (!existingAdmin) {
      // 创建默认管理员用户
      const hashedPassword = await bcrypt.hash('admin', 12)
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'admin',
          password: hashedPassword,
          name: '系统管理员',
          role: 'ADMIN',
          status: 'ACTIVE'
        }
      })
      console.log('✅ 默认管理员用户创建成功')
    } else {
      console.log('ℹ️ 管理员用户已存在')
    }

    // 检查是否已有系统设置
    const existingSettings = await prisma.systemSettings.findFirst()
    
    if (!existingSettings) {
      await prisma.systemSettings.create({
        data: {
          siteName: '创客教育平台',
          siteDescription: '专业的创客教育管理平台',
          contactEmail: '<EMAIL>',
          contactPhone: '************',
          address: '北京市朝阳区创客大厦',
          workingHours: '周一至周五 9:00-18:00',
          socialWeChat: 'maker_education',
          socialQQ: '123456789',
          maxTeamMembers: 50,
          requireApproval: true,
          allowRegistration: true,
          termsOfService: '欢迎使用创客教育平台...',
          privacyPolicy: '我们重视您的隐私...',
          aboutUs: '创客教育平台致力于...'
        }
      })
      console.log('✅ 系统设置初始化成功')
    } else {
      console.log('ℹ️ 系统设置已存在')
    }

    console.log('🎉 数据库初始化完成！')
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('数据库初始化成功完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('数据库初始化失败:', error)
      process.exit(1)
    })
}

module.exports = { initializeDatabase }
