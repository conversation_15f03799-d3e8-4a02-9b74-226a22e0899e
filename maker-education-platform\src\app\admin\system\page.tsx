'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { 
  Server, 
  Database, 
  Wifi, 
  HardDrive, 
  Cpu, 
  Activity,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Upload
} from 'lucide-react'

interface SystemStatus {
  status: 'healthy' | 'warning' | 'error'
  timestamp: string
  uptime: number
  version: string
  environment: string
  performance: {
    apiResponseTime: number
    dbResponseTime: number
    memoryUsage: number
    cpuCores: number
    loadAverage: number
  }
  resources: {
    memory: {
      total: number
      used: number
      free: number
      usage: number
    }
    cpu: {
      cores: number
      model: string
      load: number[]
    }
    disk: {
      database: number
    }
  }
  services: Array<{
    name: string
    status: string
    responseTime: number
    details: string
  }>
  database: {
    status: string
    responseTime: number
    records: {
      users: number
      projects: number
      posts: number
      applications: number
      total: number
    }
    size: number
  }
  system: {
    platform: string
    architecture: string
    nodeVersion: string
    uptime: number
    startTime: string
  }
}

export default function SystemPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchSystemStatus()
    
    // 每30秒更新一次状态
    const interval = setInterval(fetchSystemStatus, 30000)
    return () => clearInterval(interval)
  }, [session, status, router])

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/admin/system/status')
      if (response.ok) {
        const data = await response.json()
        setSystemStatus(data)
        setLastUpdate(new Date())
      } else {
        console.error('Failed to fetch system status')
      }
    } catch (error) {
      console.error('Failed to fetch system status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
      case 'connected':
        return 'text-green-600 bg-green-100'
      case 'warning':
      case 'slow':
        return 'text-yellow-600 bg-yellow-100'
      case 'error':
      case 'offline':
      case 'disconnected':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'connected':
        return <CheckCircle className="w-4 h-4" />
      case 'warning':
      case 'slow':
        return <AlertTriangle className="w-4 h-4" />
      case 'offline':
      case 'disconnected':
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <Activity className="w-4 h-4" />
    }
  }

  const getPerformanceColor = (value: number, type: 'cpu' | 'memory' | 'disk' | 'response') => {
    const thresholds = {
      cpu: { warning: 70, danger: 90 },
      memory: { warning: 80, danger: 95 },
      disk: { warning: 80, danger: 95 },
      response: { warning: 200, danger: 500 }
    }

    const threshold = thresholds[type]
    if (value >= threshold.danger) return 'bg-red-500'
    if (value >= threshold.warning) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">系统监控</h2>
            <p className="text-gray-600">实时监控系统状态和性能</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              最后更新: {lastUpdate.toLocaleTimeString('zh-CN')}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchSystemStatus}
              className="rounded-xl"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              刷新
            </Button>
          </div>
        </div>

        {systemStatus && (
          <>
            {/* 系统状态卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 服务器状态 */}
              <div className="bg-white rounded-xl shadow-enhanced p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Server className="w-5 h-5 mr-2" />
                    服务器状态
                  </h3>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(systemStatus.status)}`}>
                    {getStatusIcon(systemStatus.status)}
                    <span className="ml-1">
                      {systemStatus.status === 'healthy' ? '健康' : systemStatus.status === 'warning' ? '警告' : '错误'}
                    </span>
                  </span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">运行时间:</span>
                    <span className="font-medium">{Math.floor(systemStatus.uptime / 3600)}小时 {Math.floor((systemStatus.uptime % 3600) / 60)}分钟</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">版本:</span>
                    <span className="font-medium">{systemStatus.version}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">环境:</span>
                    <span className="font-medium">{systemStatus.environment}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">平台:</span>
                    <span className="font-medium">{systemStatus.system?.platform} {systemStatus.system?.architecture}</span>
                  </div>
                </div>
              </div>

              {/* 数据库状态 */}
              <div className="bg-white rounded-xl shadow-enhanced p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Database className="w-5 h-5 mr-2" />
                    数据库状态
                  </h3>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(systemStatus.database.status)}`}>
                    {getStatusIcon(systemStatus.database.status)}
                    <span className="ml-1">
                      {systemStatus.database.status === 'healthy' ? '健康' : '异常'}
                    </span>
                  </span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">响应时间:</span>
                    <span className="font-medium">{systemStatus.database.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">数据库大小:</span>
                    <span className="font-medium">{systemStatus.database.size}MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总记录数:</span>
                    <span className="font-medium">{systemStatus.database.records?.total || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">用户数:</span>
                    <span className="font-medium">{systemStatus.database.records?.users || 0}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 性能监控 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                性能监控
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Cpu className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {systemStatus.performance.cpu}%
                  </div>
                  <div className="text-sm text-gray-600 mb-2">CPU使用率</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.cpu, 'cpu')}`}
                      style={{ width: `${systemStatus.performance.cpu}%` }}
                    ></div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <HardDrive className="w-8 h-8 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {systemStatus.performance.memory}%
                  </div>
                  <div className="text-sm text-gray-600 mb-2">内存使用率</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.memory, 'memory')}`}
                      style={{ width: `${systemStatus.performance.memory}%` }}
                    ></div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <HardDrive className="w-8 h-8 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {systemStatus.performance.disk}%
                  </div>
                  <div className="text-sm text-gray-600 mb-2">磁盘使用率</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.disk, 'disk')}`}
                      style={{ width: `${systemStatus.performance.disk}%` }}
                    ></div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Wifi className="w-8 h-8 text-orange-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {systemStatus.performance.responseTime}ms
                  </div>
                  <div className="text-sm text-gray-600 mb-2">响应时间</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getPerformanceColor(systemStatus.performance.responseTime, 'response')}`}
                      style={{ width: `${Math.min(systemStatus.performance.responseTime / 5, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* 功能状态 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">功能状态</h3>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {Object.entries(systemStatus.features).map(([key, enabled]) => (
                  <div key={key} className="text-center p-4 border border-gray-200 rounded-lg">
                    <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 ${
                      enabled ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                    }`}>
                      {enabled ? <CheckCircle className="w-4 h-4" /> : <AlertTriangle className="w-4 h-4" />}
                    </div>
                    <div className="text-sm font-medium text-gray-900 capitalize">
                      {key === 'registration' ? '用户注册' :
                       key === 'blog' ? '博客功能' :
                       key === 'projects' ? '作品展示' :
                       key === 'team' ? '团队介绍' :
                       key === 'apply' ? '申请功能' : key}
                    </div>
                    <div className={`text-xs mt-1 ${enabled ? 'text-green-600' : 'text-red-600'}`}>
                      {enabled ? '启用' : '禁用'}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
              
              <div className="flex flex-wrap gap-4">
                <Button variant="outline" className="btn-enhanced rounded-xl">
                  <Download className="w-4 h-4 mr-2" />
                  导出数据
                </Button>
                <Button variant="outline" className="btn-enhanced rounded-xl">
                  <Upload className="w-4 h-4 mr-2" />
                  导入数据
                </Button>
                <Button variant="outline" className="btn-enhanced rounded-xl">
                  <Database className="w-4 h-4 mr-2" />
                  备份数据库
                </Button>
                <Button variant="outline" className="btn-enhanced rounded-xl">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重启服务
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  )
}
