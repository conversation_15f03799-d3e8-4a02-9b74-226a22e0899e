import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any, { params }: { params: { id: string } }) => {
  try {
    const id = await params.id

    const contact = await prisma.contact.findUnique({
      where: { id }
    })

    if (!contact) {
      return NextResponse.json(
        { error: '联系信息不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(contact)
  } catch (error) {
    console.error('Failed to fetch contact:', error)
    return NextResponse.json(
      { error: '获取联系信息失败' },
      { status: 500 }
    )
  }
})

export const PATCH = withAdmin(async (request: NextRequest, session: any, { params }: { params: { id: string } }) => {
  try {
    const id = await params.id
    const { status } = await request.json()

    if (!['PENDING', 'REPLIED', 'CLOSED'].includes(status)) {
      return NextResponse.json(
        { error: '无效的状态' },
        { status: 400 }
      )
    }

    const contact = await prisma.contact.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date()
      }
    })

    return NextResponse.json(contact)
  } catch (error) {
    console.error('Failed to update contact:', error)
    return NextResponse.json(
      { error: '更新联系信息失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAdmin(async (request: NextRequest, session: any, { params }: { params: { id: string } }) => {
  try {
    const id = await params.id

    await prisma.contact.delete({
      where: { id }
    })

    return NextResponse.json({ message: '联系信息删除成功' })
  } catch (error) {
    console.error('Failed to delete contact:', error)
    return NextResponse.json(
      { error: '删除联系信息失败' },
      { status: 500 }
    )
  }
})
