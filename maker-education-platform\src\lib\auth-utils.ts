import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { NextRequest, NextResponse } from 'next/server'

export async function getSession() {
  return await getServerSession(authOptions)
}

export async function requireAuth() {
  const session = await getSession()
  if (!session) {
    throw new Error('未授权访问')
  }
  return session
}

export async function requireAdmin() {
  const session = await requireAuth()
  if (session.user.role !== 'ADMIN') {
    throw new Error('需要管理员权限')
  }
  return session
}

export function withAuth(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    try {
      const session = await requireAuth()
      return await handler(request, session, context)
    } catch (error) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }
  }
}

export function withAdmin(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    try {
      const session = await requireAdmin()
      return await handler(request, session, context)
    } catch (error) {
      console.error('Admin auth error:', error)
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      )
    }
  }
}
