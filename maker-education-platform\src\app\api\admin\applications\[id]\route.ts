import { NextRequest, NextResponse } from 'next/server'
import { safeDbOperation } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params

  const application = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')
    return await prisma.application.findUnique({
      where: { id },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })
  }, null)

  if (!application) {
    return NextResponse.json(
      { error: '申请未找到' },
      { status: 404 }
    )
  }

  // 解析JSON字符串为数组
  const processedApplication = {
    ...application,
    skills: application.skills ? JSON.parse(application.skills) : []
  }

  return NextResponse.json(processedApplication)
})

export const PATCH = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  const { status, reviewNote } = await request.json()

  if (!['APPROVED', 'REJECTED'].includes(status)) {
    return NextResponse.json(
      { error: '无效的审核状态' },
      { status: 400 }
    )
  }

  const result = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')

    const application = await prisma.application.update({
      where: { id },
      data: {
        status,
        reviewNote,
        reviewedBy: session.user.id,
        reviewedAt: new Date()
      },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 如果申请通过，自动创建团队成员记录
    if (status === 'APPROVED') {
      try {
        // 检查是否已经是团队成员
        const existingMember = await prisma.teamMember.findFirst({
          where: {
            OR: [
              { userId: application.applicantId },
              { name: application.name }
            ]
          }
        })

        if (!existingMember) {
          // 自动创建团队成员记录
          await prisma.teamMember.create({
            data: {
              userId: application.applicantId,
              name: application.name,
              position: application.position,
              bio: application.motivation,
              skills: application.skills, // 已经是JSON字符串
              isActive: true,
              displayOrder: 0,
              joinDate: new Date()
            }
          })

          console.log(`申请通过，自动创建团队成员: ${application.name}`)
        }
      } catch (error) {
        console.error('自动创建团队成员失败:', error)
        // 不影响申请审核的主流程
      }
    }

    return application
  }, null)

  if (!result) {
    return NextResponse.json(
      { error: '审核申请失败' },
      { status: 500 }
    )
  }

  // 解析JSON字符串为数组
  const processedApplication = {
    ...result,
    skills: result.skills ? JSON.parse(result.skills) : []
  }

  return NextResponse.json(processedApplication)
})

export const DELETE = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params

  const result = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')
    await prisma.application.delete({
      where: { id }
    })
    return true
  }, false)

  if (result) {
    return NextResponse.json({ message: '申请删除成功' })
  } else {
    return NextResponse.json(
      { error: '删除申请失败' },
      { status: 500 }
    )
  }
})
