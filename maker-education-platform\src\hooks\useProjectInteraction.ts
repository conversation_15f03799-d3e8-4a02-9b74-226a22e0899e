'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface ProjectInteractionState {
  liked: boolean
  likeCount: number
  viewCount: number
  shareCount: number
  isLoading: boolean
  error: string | null
}

export function useProjectInteraction(projectId: string) {
  const { data: session } = useSession()
  const [state, setState] = useState<ProjectInteractionState>({
    liked: false,
    likeCount: 0,
    viewCount: 0,
    shareCount: 0,
    isLoading: true,
    error: null
  })

  // 获取初始状态
  useEffect(() => {
    if (!projectId) return

    const fetchInitialState = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }))

        // 获取项目基本信息
        const projectResponse = await fetch(`/api/projects/${projectId}`)
        const projectData = await projectResponse.json()

        setState(prev => ({
          ...prev,
          liked: false, // 暂时禁用点赞功能
          likeCount: projectData.likeCount || 0,
          viewCount: projectData.viewCount || 0,
          shareCount: projectData.shareCount || 0,
          isLoading: false
        }))
      } catch (error) {
        console.error('获取项目互动状态失败:', error)
        setState(prev => ({
          ...prev,
          error: '获取数据失败',
          isLoading: false
        }))
      }
    }

    fetchInitialState()
  }, [projectId])

  // 点赞/取消点赞 (暂时禁用)
  const toggleLike = async () => {
    setState(prev => ({ ...prev, error: '点赞功能正在维护中' }))
  }

  // 分享
  const share = async (platform: string) => {
    try {
      setState(prev => ({
        ...prev,
        error: null,
        shareCount: prev.shareCount + 1
      }))
    } catch (error) {
      console.error('分享失败:', error)
      setState(prev => ({ ...prev, error: '分享失败，请稍后重试' }))
    }
  }

  // 清除错误
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }))
  }

  return {
    ...state,
    toggleLike,
    share,
    clearError,
    isLoggedIn: !!session
  }
}
