# Railway 部署指南（无需GitHub）

## 🚀 快速部署到Railway

### 准备工作
1. 注册Railway账户：https://railway.app （使用邮箱注册）
2. 注册Supabase账户：https://supabase.com （提供免费PostgreSQL数据库）

### 第一步：设置Supabase数据库

1. 访问 https://supabase.com
2. 点击 "Start your project" 注册账户
3. 创建新项目：
   - 项目名称：`maker-education-db`
   - 数据库密码：设置一个强密码（记住这个密码）
   - 地区：选择离您最近的区域（如Asia Pacific）
4. 创建完成后，等待数据库初始化（约1-2分钟）

5. 获取数据库连接信息：
   - 在Supabase项目页面，点击左侧菜单 "Settings" → "Database"
   - 找到 "Connection string" 部分
   - 复制 "URI" 格式的连接字符串
   - 格式类似：`postgresql://postgres:[YOUR-PASSWORD]@db.xxx.supabase.co:5432/postgres`

### 第二步：部署到Railway

#### 方法一：使用Railway CLI（推荐）

1. 安装Railway CLI：
   ```bash
   npm install -g @railway/cli
   ```

2. 登录Railway：
   ```bash
   railway login
   ```

3. 在项目目录中初始化：
   ```bash
   cd maker-education-platform
   railway init
   ```

4. 部署项目：
   ```bash
   railway up
   ```

#### 方法二：使用Railway网页界面

1. 访问 https://railway.app
2. 注册账户（使用邮箱）
3. 点击 "Start a New Project"
4. 选择 "Deploy from GitHub" 或 "Deploy from Template"
5. 如果选择模板，选择 "Node.js" 模板

### 第三步：配置环境变量

在Railway项目设置中，添加以下环境变量：

**DATABASE_URL**
```
您从Supabase复制的数据库连接字符串
```

**NEXTAUTH_SECRET**
```
your-super-secret-key-change-this-in-production-32chars
```

**NEXTAUTH_URL**
```
https://您的项目名.railway.app
```
（注意：项目名会在部署后显示，先填写一个临时的，部署后再更新）

### 第四步：更新NEXTAUTH_URL

1. 部署完成后，Railway会显示您的网站URL
2. 回到Railway项目设置
3. 点击 "Variables" 标签
4. 编辑 NEXTAUTH_URL，改为您的实际域名
5. 重新部署：点击 "Deployments" → 最新部署旁的三个点 → "Redeploy"

### 第五步：初始化数据库

部署成功后，需要初始化数据库：

1. 在Railway项目页面，点击 "Deployments"
2. 找到最新的部署，点击 "View Logs"
3. 在日志中查看数据库初始化情况
4. 如果看到错误，可能需要手动运行数据库迁移

### 第六步：验证部署

1. 访问您的网站URL
2. 测试功能：
   - 首页加载
   - 用户注册：创建一个测试账户
   - 管理员登录：用户名 `admin`，密码 `admin`
   - 管理后台功能

## 🔧 故障排除

### 常见问题

**1. 构建失败**
- 检查package.json文件是否完整
- 确保所有依赖都已安装

**2. 数据库连接错误**
- 验证DATABASE_URL格式正确
- 确保Supabase数据库正在运行
- 检查数据库密码是否正确

**3. 认证问题**
- 检查NEXTAUTH_SECRET是否设置
- 确保NEXTAUTH_URL与实际域名匹配

**4. 页面404错误**
- 检查路由配置
- 确保所有页面文件都已上传

### 环境变量示例

```env
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
NEXTAUTH_SECRET="your-super-secret-key-change-this-in-production-32chars"
NEXTAUTH_URL="https://your-project-name.railway.app"
```

## 🎉 部署成功！

部署成功后，您将拥有：
- ✅ 完全功能的创客教育平台
- ✅ 免费的PostgreSQL数据库
- ✅ 全球CDN加速
- ✅ 自动HTTPS证书
- ✅ 自动部署（代码更新时）

## 📞 需要帮助？

如果遇到问题，请检查：
1. Railway部署日志
2. 浏览器开发者工具控制台
3. 数据库连接状态

默认管理员账户：
- 用户名：admin
- 密码：admin

## 💰 费用说明

- **Railway**: 免费额度包括每月500小时运行时间
- **Supabase**: 免费额度包括每月500MB数据库存储
- 对于小型项目，这些免费额度通常足够使用 