{"name": "maker-education-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:push": "prisma db push", "db:generate": "prisma generate", "db:init": "node scripts/init-db.js", "postinstall": "prisma generate", "vercel-build": "prisma generate && prisma db push && node scripts/init-db.js && next build"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.11.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html-to-image": "^1.11.13", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}