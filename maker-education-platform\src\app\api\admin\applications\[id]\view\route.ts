import { NextRequest, NextResponse } from 'next/server'
import { safeDbOperation } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  const application = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')
    return await prisma.application.findUnique({
      where: { id },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })
  }, null)

  if (!application) {
    return NextResponse.json(
      { error: '申请未找到' },
      { status: 404 }
    )
  }

  // 解析JSON字符串为数组
  const processedApplication = {
    ...application,
    skills: application.skills ? JSON.parse(application.skills) : []
  }

  return NextResponse.json(processedApplication)
})
