# 🚀 Netlify 直接部署指南

## 方法：拖拽部署（无需GitHub）

### 第一步：准备文件
1. 将整个项目文件夹压缩成 ZIP 文件
2. 确保包含以下文件：
   - src/ 文件夹
   - package.json
   - next.config.js
   - 其他配置文件

### 第二步：部署到Netlify
1. 访问：https://netlify.com
2. 注册账户（可以用邮箱）
3. 点击 "Sites" → "Add new site" → "Deploy manually"
4. 直接拖拽你的 ZIP 文件到页面
5. 等待部署完成

### 第三步：配置环境变量
1. 在Netlify项目中，点击 "Site settings"
2. 点击 "Environment variables"
3. 添加：
   - DATABASE_URL: 你的数据库连接
   - NEXTAUTH_SECRET: 密钥
   - NEXTAUTH_URL: 你的Netlify域名

### 优点：
✅ 无需GitHub
✅ 直接拖拽部署
✅ 免费额度充足
✅ 自动HTTPS
