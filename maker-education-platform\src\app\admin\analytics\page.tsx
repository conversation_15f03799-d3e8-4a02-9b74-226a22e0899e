'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { TrendingUp, Users, FileText, Eye, Calendar, Award } from 'lucide-react'

interface AnalyticsData {
  totalUsers: number
  totalProjects: number
  totalPosts: number
  totalViews: number
  totalApplications: number
  userGrowth: number
  projectGrowth: number
  postGrowth: number
  popularProjects: Array<{
    id: string
    title: string
    viewCount: number
    category: string
  }>
  popularPosts: Array<{
    id: string
    title: string
    viewCount: number
    author: {
      name: string
    }
  }>
  recentActivity: Array<{
    type: string
    description: string
    timestamp: string
  }>
  timeRange: string
  stats: {
    avgProjectViews: number
    activeUsersToday: number
    publishedContentToday: number
  }
}

export default function AnalyticsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchAnalytics()
  }, [session, status, router, timeRange])

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(`/api/admin/analytics?timeRange=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data)
      } else {
        console.error('Failed to fetch analytics')
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registration':
        return <Users className="w-4 h-4 text-blue-600" />
      case 'project_published':
        return <FileText className="w-4 h-4 text-green-600" />
      case 'post_published':
        return <FileText className="w-4 h-4 text-indigo-600" />
      case 'application_submit':
        return <Award className="w-4 h-4 text-purple-600" />
      default:
        return <Calendar className="w-4 h-4 text-gray-600" />
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">数据分析</h2>
            <p className="text-gray-600">查看平台运营数据和统计信息</p>
          </div>
          <div className="flex gap-2">
            {[
              { value: '7d', label: '7天' },
              { value: '30d', label: '30天' },
              { value: '90d', label: '90天' },
            ].map((range) => (
              <button
                key={range.value}
                onClick={() => setTimeRange(range.value)}
                className={`px-4 py-2 rounded-xl text-sm font-medium transition-colors ${
                  timeRange === range.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总用户数</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.totalUsers}</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  +{analytics?.userGrowth}%
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-xl">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总作品数</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.totalProjects}</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  +{analytics?.projectGrowth}%
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-xl">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总浏览量</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.totalViews}</p>
                <p className="text-sm text-gray-500 mt-1">本月数据</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-xl">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">发布文章</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.totalPosts}</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  +{analytics?.postGrowth}%
                </p>
              </div>
              <div className="p-3 bg-indigo-100 rounded-xl">
                <FileText className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">申请数量</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.totalApplications}</p>
                <p className="text-sm text-gray-500 mt-1">总申请数</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-xl">
                <Award className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Popular Projects */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">热门作品</h3>
            <div className="space-y-4">
              {analytics?.popularProjects?.slice(0, 5).map((project, index) => (
                <div key={project.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{project.title}</p>
                      <p className="text-xs text-gray-500">{project.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Eye className="w-4 h-4 mr-1" />
                    {project.viewCount}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Popular Posts */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">热门文章</h3>
            <div className="space-y-4">
              {analytics?.popularPosts?.slice(0, 5).map((post, index) => (
                <div key={post.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{post.title}</p>
                      <p className="text-xs text-gray-500">by {post.author.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Eye className="w-4 h-4 mr-1" />
                    {post.viewCount}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-xl shadow-enhanced p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
            <div className="space-y-4">
              {analytics?.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="mr-3">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(activity.timestamp).toLocaleString('zh-CN')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Chart Placeholder */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">访问趋势</h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">图表功能开发中...</p>
              <p className="text-sm text-gray-400">可集成 Chart.js 或其他图表库</p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
