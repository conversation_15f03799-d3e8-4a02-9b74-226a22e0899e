# 创客教育平台部署指南

## 🚀 快速部署到Vercel（推荐）

### 准备工作
1. 注册GitHub账户：https://github.com
2. 注册Vercel账户：https://vercel.com （使用GitHub登录）
3. 注册Neon数据库：https://neon.tech （使用GitHub登录）

### 第一步：上传代码到GitHub

#### 方法一：使用GitHub网页界面（推荐）
1. 访问 https://github.com/new
2. 仓库名称：`maker-education-platform`
3. 设置为 Public
4. 不要勾选 "Add a README file"
5. 点击 "Create repository"

6. 在新建的仓库页面，点击 "uploading an existing file"
7. 将项目文件夹中的所有文件拖拽上传（除了node_modules文件夹）
8. 提交信息写：`Initial commit`
9. 点击 "Commit changes"

#### 方法二：使用Git命令（如果已安装Git）
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/你的用户名/maker-education-platform.git
git push -u origin main
```

### 第二步：设置免费数据库

1. 访问 https://neon.tech
2. 点击 "Sign up" 并使用GitHub账户登录
3. 创建新项目：
   - Project name: `maker-education-db`
   - Region: 选择离你最近的区域
   - PostgreSQL version: 保持默认
4. 创建完成后，复制连接字符串（类似这样）：
   ```
   postgresql://username:<EMAIL>/database_name?sslmode=require
   ```

### 第三步：部署到Vercel

1. 访问 https://vercel.com
2. 使用GitHub账户登录
3. 点击 "New Project"
4. 选择你刚创建的 `maker-education-platform` 仓库
5. 点击 "Import"

6. 在配置页面：
   - Framework Preset: Next.js
   - Root Directory: ./
   - Build Command: `npm run build`
   - Output Directory: 保持默认
   - Install Command: `npm install`

7. 展开 "Environment Variables" 部分，添加以下变量：

   **DATABASE_URL**
   ```
   你从Neon复制的数据库连接字符串
   ```

   **NEXTAUTH_SECRET**
   ```
   your-super-secret-key-change-this-in-production-32chars
   ```

   **NEXTAUTH_URL**
   ```
   https://你的项目名.vercel.app
   ```
   （注意：项目名会在部署后显示，先填写一个临时的，部署后再更新）

8. 点击 "Deploy"

### 第四步：更新NEXTAUTH_URL

1. 部署完成后，Vercel会显示你的网站URL（如：https://maker-education-platform-xxx.vercel.app）
2. 回到Vercel项目设置
3. 点击 "Settings" → "Environment Variables"
4. 编辑 NEXTAUTH_URL，改为你的实际域名
5. 重新部署：点击 "Deployments" → 最新部署旁的三个点 → "Redeploy"

### 第五步：验证部署

1. 访问你的网站URL
2. 测试功能：
   - 首页加载
   - 用户注册：创建一个测试账户
   - 管理员登录：用户名 `admin`，密码 `admin`
   - 管理后台功能

## 🔧 故障排除

### 常见问题

**1. 构建失败**
- 检查package.json文件是否完整
- 确保所有依赖都已安装

**2. 数据库连接错误**
- 验证DATABASE_URL格式正确
- 确保Neon数据库正在运行

**3. 认证问题**
- 检查NEXTAUTH_SECRET是否设置
- 确保NEXTAUTH_URL与实际域名匹配

**4. 页面404错误**
- 检查路由配置
- 确保所有页面文件都已上传

### 环境变量示例

```env
DATABASE_URL="postgresql://username:<EMAIL>/database_name?sslmode=require"
NEXTAUTH_SECRET="your-super-secret-key-change-this-in-production-32chars"
NEXTAUTH_URL="https://your-project-name.vercel.app"
```

## 🎉 部署成功！

部署成功后，你将拥有：
- ✅ 完全功能的创客教育平台
- ✅ 免费的PostgreSQL数据库
- ✅ 全球CDN加速
- ✅ 自动HTTPS证书
- ✅ 自动部署（代码更新时）

## 📞 需要帮助？

如果遇到问题，请检查：
1. Vercel部署日志
2. 浏览器开发者工具控制台
3. 数据库连接状态

默认管理员账户：
- 用户名：admin
- 密码：admin
