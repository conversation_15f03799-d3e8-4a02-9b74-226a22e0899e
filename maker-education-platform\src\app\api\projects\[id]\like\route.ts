import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }
    const userId = session.user.id

    // 检查项目是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 检查用户是否已经点赞
    const existingLike = await prisma.projectLike.findUnique({
      where: {
        userId_projectId: {
          userId,
          projectId
        }
      }
    })

    if (existingLike) {
      // 取消点赞
      await prisma.projectLike.delete({
        where: { id: existingLike.id }
      })

      // 更新点赞计数
      const updatedProject = await prisma.project.update({
        where: { id: projectId },
        data: {
          likeCount: {
            decrement: 1
          }
        }
      })

      return NextResponse.json({
        liked: false,
        likeCount: updatedProject.likeCount
      })
    } else {
      // 添加点赞
      await prisma.projectLike.create({
        data: {
          userId,
          projectId
        }
      })

      // 更新点赞计数
      const updatedProject = await prisma.project.update({
        where: { id: projectId },
        data: {
          likeCount: {
            increment: 1
          }
        }
      })

      return NextResponse.json({
        liked: true,
        likeCount: updatedProject.likeCount
      })
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    return NextResponse.json(
      { error: '操作失败，请稍后重试' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({
        liked: false,
        likeCount: 0
      })
    }

    const userId = session.user.id

    // 获取项目信息和用户点赞状态
    const [project, userLike] = await Promise.all([
      prisma.project.findUnique({
        where: { id: projectId },
        select: { likeCount: true }
      }),
      // 暂时注释掉新模型的查询，直到Prisma客户端重新生成
      // prisma.projectLike.findUnique({
        where: {
          userId_projectId: {
            userId,
            projectId
          }
        }
      })
    ])

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      liked: !!userLike,
      likeCount: project.likeCount
    })
  } catch (error) {
    console.error('获取点赞状态失败:', error)
    return NextResponse.json(
      { error: '获取数据失败' },
      { status: 500 }
    )
  }
}
