import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'

// 分类映射
const categoryMap: { [key: string]: string } = {
  'STEAM_EDUCATION': 'STEAM教育',
  'THREE_D_PRINTING': '3D打印',
  'ROBOTICS': '机器人',
  'PROGRAMMING': '编程',
  'ELECTRONICS': '电子',
  'CRAFTS': '手工制作',
  'MAKER_SPACE': '创客空间',
  'OTHER': '其他'
}

// 反向映射（中文到枚举值）
const reverseCategoryMap: { [key: string]: string } = {
  'STEAM教育': 'STEAM_EDUCATION',
  '3D打印': 'THREE_D_PRINTING',
  '机器人': 'ROBOTICS',
  '机器人制作': 'ROBOTICS',
  '编程': 'PROGRAMMING',
  '编程项目': 'PROGRAMMING',
  '电子': 'ELECTRONICS',
  '电子制作': 'ELECTRONICS',
  '手工制作': 'CRAFTS',
  '创意设计': 'CRAFTS',
  '创客空间': 'MAKER_SPACE',
  '其他': 'OTHER'
}

export const GET = withAuth(async (request: NextRequest, session: any) => {
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const status = searchParams.get('status')
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')
    const page = searchParams.get('page') || '1'
    const search = searchParams.get('search')

    const pageSize = limit ? parseInt(limit) : 50
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {}

    if (category && category !== 'ALL') {
      where.category = category
    }

    if (status && status !== 'ALL') {
      where.status = status
    }

    if (featured === 'true') {
      where.featured = true
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } }
      ]
    }

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: pageSize,
      }),
      prisma.project.count({ where })
    ])

    // 解析JSON字符串为数组，并转换分类显示名称
    const processedProjects = projects.map(project => ({
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : [],
      categoryDisplay: categoryMap[project.category] || project.category
    }))

    return NextResponse.json({
      projects: processedProjects,
      pagination: {
        page: parseInt(page),
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('Failed to fetch projects:', error)
    return NextResponse.json(
      { error: '获取作品列表失败' },
      { status: 500 }
    )
  }
})

export const POST = withAuth(async (request: NextRequest, session: any) => {
  // 只有管理员可以访问
  if (session.user.role !== 'ADMIN') {
    return NextResponse.json(
      { error: '无权限访问' },
      { status: 403 }
    )
  }

  try {
    const {
      title,
      description,
      content,
      images,
      category,
      tags,
      featured = false,
      status = 'PUBLISHED',
      authorId
    } = await request.json()

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: '标题、描述和分类为必填项' },
        { status: 400 }
      )
    }

    // 转换分类为枚举值
    const categoryEnum = reverseCategoryMap[category] || category

    // 如果没有指定作者，使用当前管理员
    const finalAuthorId = authorId || session.user.id

    const project = await prisma.project.create({
      data: {
        title,
        description,
        content,
        images: JSON.stringify(images || []),
        category: categoryEnum,
        tags: JSON.stringify(tags || []),
        featured,
        status,
        authorId: finalAuthorId
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组返回，并转换分类显示名称
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : [],
      categoryDisplay: categoryMap[project.category] || project.category
    }

    return NextResponse.json(processedProject, { status: 201 })
  } catch (error) {
    console.error('Failed to create project:', error)
    return NextResponse.json(
      { error: '创建作品失败' },
      { status: 500 }
    )
  }
})
