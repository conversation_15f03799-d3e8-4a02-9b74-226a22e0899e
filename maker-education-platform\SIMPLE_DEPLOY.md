# 超简单部署指南（无需GitHub）

## 🎯 最简单方案：Netlify + Supabase

### 第一步：注册账户（2分钟）
1. **Netlify**: https://netlify.com （邮箱注册）
2. **Supabase**: https://supabase.com （邮箱注册）

### 第二步：设置数据库（5分钟）
1. 访问 https://supabase.com
2. 点击 "Start your project"
3. 注册账户
4. 创建新项目：
   - 项目名称：`maker-education-db`
   - 密码：设置一个密码（记住它）
   - 地区：选择 Asia Pacific
5. 等待创建完成（1-2分钟）
6. 复制数据库连接字符串：
   - 点击左侧 "Settings" → "Database"
   - 复制 "URI" 格式的连接字符串

### 第三步：部署到Netlify（3分钟）
1. 访问 https://netlify.com
2. 注册账户
3. 点击 "Add new site" → "Deploy manually"
4. 将整个 `maker-education-platform` 文件夹拖拽到上传区域
5. 等待部署完成

### 第四步：设置环境变量（2分钟）
1. 在Netlify项目页面，点击 "Site settings"
2. 点击 "Environment variables"
3. 添加以下变量：

**DATABASE_URL**
```
您从Supabase复制的连接字符串
```

**NEXTAUTH_SECRET**
```
your-secret-key-32-chars-long
```

**NEXTAUTH_URL**
```
https://您的项目名.netlify.app
```

### 第五步：重新部署（1分钟）
1. 点击 "Deploys" 标签
2. 点击 "Trigger deploy" → "Deploy site"

## 🎉 完成！

您的网站现在应该可以访问了！

### 测试网站
1. 访问您的Netlify域名
2. 测试注册功能
3. 管理员登录：用户名 `admin`，密码 `admin`

## 🔧 如果遇到问题

**网站无法访问**
- 检查环境变量是否正确设置
- 重新部署一次

**数据库连接错误**
- 确认Supabase数据库正在运行
- 检查DATABASE_URL格式

**登录问题**
- 确认NEXTAUTH_SECRET已设置
- 检查NEXTAUTH_URL是否正确

## 💰 费用
- **Netlify**: 完全免费
- **Supabase**: 免费额度500MB，足够使用

## 📞 需要帮助？
如果遇到问题，请告诉我具体的错误信息！ 