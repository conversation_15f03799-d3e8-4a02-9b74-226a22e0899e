{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "outputDirectory": ".next", "env": {"NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url", "DATABASE_URL": "@database_url"}, "build": {"env": {"NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url", "DATABASE_URL": "@database_url"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}}