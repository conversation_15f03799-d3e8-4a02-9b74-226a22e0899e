ROSTemplateFormatVersion: '2015-09-01'
Transform: 'Aliyun::Serverless-2018-04-03'
Resources:
  maker-education-platform:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: '创客教育平台'
      Policies:
        - Version: '1'
          Statement:
            - Effect: Allow
              Action:
                - 'oss:GetObject'
                - 'oss:PutObject'
              Resource: '*'
    maker-education-app:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Description: '创客教育平台应用'
        Runtime: nodejs18
        CodeUri: ./
        Handler: index.handler
        Timeout: 30
        MemorySize: 512
        EnvironmentVariables:
          NODE_ENV: production 