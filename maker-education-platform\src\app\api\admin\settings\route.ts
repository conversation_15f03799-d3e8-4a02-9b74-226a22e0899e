import { NextRequest, NextResponse } from 'next/server'
import { safeDbOperation } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

// 默认系统设置
const defaultSettings = [
  // 基本信息
  { key: 'site_name', value: '创客教育平台', type: 'STRING', description: '网站名称', category: 'GENERAL' },
  { key: 'site_description', value: '专业的STEAM教育平台', type: 'STRING', description: '网站描述', category: 'GENERAL' },
  { key: 'site_slogan', value: '创新思维 · 实践能力', type: 'STRING', description: '网站标语', category: 'GENERAL' },
  { key: 'team_count', value: '50', type: 'NUMBER', description: '团队人数', category: 'GENERAL' },
  { key: 'copyright_year', value: '2024', type: 'STRING', description: '版权年份', category: 'GENERAL' },

  // 联系信息
  { key: 'contact_phone', value: '************', type: 'STRING', description: '联系电话', category: 'CONTACT' },
  { key: 'contact_email', value: '<EMAIL>', type: 'STRING', description: '联系邮箱', category: 'CONTACT' },
  { key: 'contact_address', value: '北京市海淀区创新大厦', type: 'STRING', description: '联系地址', category: 'CONTACT' },
  { key: 'contact_wechat', value: 'makeredu2024', type: 'STRING', description: '微信号', category: 'CONTACT' },
  { key: 'contact_qq', value: '123456789', type: 'STRING', description: 'QQ号', category: 'CONTACT' },

  // 功能开关
  { key: 'allow_registration', value: 'true', type: 'BOOLEAN', description: '允许用户注册', category: 'FEATURES' },
  { key: 'require_approval', value: 'true', type: 'BOOLEAN', description: '申请需要审批', category: 'FEATURES' },
  { key: 'maintenance_mode', value: 'false', type: 'BOOLEAN', description: '维护模式', category: 'FEATURES' },
  { key: 'show_team_section', value: 'true', type: 'BOOLEAN', description: '显示团队介绍', category: 'FEATURES' },
  { key: 'show_projects_section', value: 'true', type: 'BOOLEAN', description: '显示作品展示', category: 'FEATURES' },
  { key: 'show_blog_section', value: 'true', type: 'BOOLEAN', description: '显示博客功能', category: 'FEATURES' },
  { key: 'show_apply_section', value: 'true', type: 'BOOLEAN', description: '显示申请功能', category: 'FEATURES' },
  { key: 'enable_comments', value: 'true', type: 'BOOLEAN', description: '启用评论功能', category: 'FEATURES' },
  { key: 'enable_likes', value: 'true', type: 'BOOLEAN', description: '启用点赞功能', category: 'FEATURES' },

  // 文件设置
  { key: 'max_file_size', value: '10', type: 'NUMBER', description: '最大文件大小(MB)', category: 'FILES' },
  { key: 'allowed_file_types', value: 'jpg,jpeg,png,gif,pdf,doc,docx', type: 'STRING', description: '允许的文件类型', category: 'FILES' },

  // 外观设置
  { key: 'primary_color', value: '#3B82F6', type: 'STRING', description: '主题色', category: 'APPEARANCE' },
  { key: 'secondary_color', value: '#6366F1', type: 'STRING', description: '辅助色', category: 'APPEARANCE' },
  { key: 'hero_background', value: 'gradient', type: 'STRING', description: '首页背景样式', category: 'APPEARANCE' },
  { key: 'show_animations', value: 'true', type: 'BOOLEAN', description: '显示动画效果', category: 'APPEARANCE' },
  { key: 'dark_mode_enabled', value: 'false', type: 'BOOLEAN', description: '启用暗色模式', category: 'APPEARANCE' },

  // 首页内容
  { key: 'hero_title', value: '创新思维 · 实践能力', type: 'STRING', description: '首页标题', category: 'CONTENT' },
  { key: 'hero_subtitle', value: 'STEAM创客教育平台', type: 'STRING', description: '首页副标题', category: 'CONTENT' },
  { key: 'hero_description', value: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。', type: 'STRING', description: '首页描述', category: 'CONTENT' },
  { key: 'about_intro', value: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。', type: 'STRING', description: '关于我们简介', category: 'CONTENT' },

  // 统计数据
  { key: 'stats_projects', value: '500', type: 'NUMBER', description: '学生作品数', category: 'STATS' },
  { key: 'stats_students', value: '1000', type: 'NUMBER', description: '培训学员数', category: 'STATS' },
  { key: 'stats_satisfaction', value: '98', type: 'NUMBER', description: '满意度评价(%)', category: 'STATS' },

  // SEO设置
  { key: 'meta_keywords', value: 'STEAM教育,创客教育,3D打印,机器人,编程', type: 'STRING', description: 'SEO关键词', category: 'SEO' },
  { key: 'meta_description', value: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程', type: 'STRING', description: 'SEO描述', category: 'SEO' },
]

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category')

  const where = category ? { category } : {}

  const settings = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')

    let settings = await prisma.setting.findMany({
      where,
      orderBy: { key: 'asc' }
    })

    // 如果没有设置，创建默认设置
    if (settings.length === 0) {
      const settingsToCreate = category
        ? defaultSettings.filter(s => s.category === category)
        : defaultSettings

      for (const setting of settingsToCreate) {
        await prisma.setting.upsert({
          where: { key: setting.key },
          update: {},
          create: setting
        })
      }

      settings = await prisma.setting.findMany({
        where,
        orderBy: { key: 'asc' }
      })
    }

    return settings
  }, [])

  // 转换为键值对格式
  const settingsMap = settings.reduce((acc, setting) => {
    let value: any = setting.value
    if (setting.type === 'BOOLEAN') {
      value = setting.value === 'true'
    } else if (setting.type === 'NUMBER') {
      value = parseFloat(setting.value)
    }
    acc[setting.key] = {
      value,
      type: setting.type,
      description: setting.description,
      category: setting.category
    }
    return acc
  }, {} as any)

  return NextResponse.json(settingsMap, {
    headers: {
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
})

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  const settings = await request.json()

  const result = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')

    // 批量更新设置
    for (const [key, data] of Object.entries(settings)) {
      const { value, type, description, category } = data as any

      let stringValue = String(value)
      if (type === 'BOOLEAN') {
        stringValue = value ? 'true' : 'false'
      }

      await prisma.setting.upsert({
        where: { key },
        update: {
          value: stringValue,
          type,
          description,
          category,
          updatedAt: new Date()
        },
        create: {
          key,
          value: stringValue,
          type: type || 'STRING',
          description,
          category: category || 'GENERAL'
        }
      })
    }

    return true
  }, false)

  if (result) {
    return NextResponse.json({ message: '设置保存成功' })
  } else {
    return NextResponse.json(
      { error: '设置保存失败' },
      { status: 500 }
    )
  }
})
