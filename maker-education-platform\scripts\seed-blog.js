const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const samplePosts = [
  {
    title: '创客教育在课堂中的应用',
    slug: 'integrating-maker-education-in-classroom',
    excerpt: '探讨如何将创客教育理念融入传统课堂教学，提升学生的创新能力和实践技能。',
    content: `
      <h2>什么是创客教育？</h2>
      <p>创客教育是一种以培养学生创新精神和实践能力为目标的教育理念。它强调"做中学"，鼓励学生通过动手实践来学习知识和技能。</p>
      
      <h2>创客教育的核心特点</h2>
      <ul>
        <li><strong>项目导向</strong>：以实际项目为载体，让学生在解决问题的过程中学习</li>
        <li><strong>跨学科融合</strong>：整合STEAM各学科知识，培养综合能力</li>
        <li><strong>协作学习</strong>：鼓励团队合作，培养沟通协调能力</li>
        <li><strong>创新思维</strong>：激发学生的创造力和想象力</li>
      </ul>
      
      <h2>在课堂中的实施策略</h2>
      <p>1. <strong>创设真实情境</strong>：选择贴近学生生活的问题作为项目主题</p>
      <p>2. <strong>提供充足资源</strong>：配备必要的工具、材料和技术支持</p>
      <p>3. <strong>引导探究过程</strong>：教师作为引导者，帮助学生发现和解决问题</p>
      <p>4. <strong>展示分享成果</strong>：为学生提供展示作品的平台，增强成就感</p>
      
      <h2>成功案例分享</h2>
      <p>在我们的创客教室中，学生们完成了许多精彩的项目，如智能垃圾分类机器人、环保监测装置等。这些项目不仅让学生掌握了技术技能，更重要的是培养了他们发现问题、分析问题和解决问题的能力。</p>
    `,
    tags: JSON.stringify(['创客教育', '课堂实践', '教学方法', 'STEAM教育']),
    published: true,
    featured: true
  },
  {
    title: 'STEAM教育的重要性与实践',
    slug: 'importance-of-steam-education',
    excerpt: '深入分析STEAM教育对学生全面发展的重要意义，以及如何在教学中有效实施。',
    content: `
      <h2>STEAM教育的内涵</h2>
      <p>STEAM教育是Science（科学）、Technology（技术）、Engineering（工程）、Arts（艺术）和Mathematics（数学）的缩写，代表了一种跨学科的教育理念。</p>
      
      <h2>为什么STEAM教育如此重要？</h2>
      <h3>1. 适应未来社会需求</h3>
      <p>21世纪是科技快速发展的时代，社会需要具备综合素养的创新型人才。STEAM教育正是培养这类人才的有效途径。</p>
      
      <h3>2. 培养核心素养</h3>
      <p>通过STEAM教育，学生可以培养：</p>
      <ul>
        <li>批判性思维</li>
        <li>创新能力</li>
        <li>协作精神</li>
        <li>沟通表达能力</li>
        <li>问题解决能力</li>
      </ul>
      
      <h3>3. 激发学习兴趣</h3>
      <p>STEAM教育通过项目式学习，让抽象的知识变得具体可感，大大提高了学生的学习积极性。</p>
      
      <h2>实践策略</h2>
      <p><strong>项目设计</strong>：选择具有挑战性和现实意义的项目主题</p>
      <p><strong>资源整合</strong>：充分利用学校和社区资源，为学生提供丰富的学习材料</p>
      <p><strong>评价改革</strong>：建立多元化的评价体系，关注过程而非仅仅结果</p>
      <p><strong>教师培训</strong>：提升教师的跨学科教学能力和项目指导水平</p>
    `,
    tags: JSON.stringify(['STEAM教育', '教育理念', '核心素养', '创新教育']),
    published: true,
    featured: false
  },
  {
    title: '3D打印技术在教育中的应用前景',
    slug: '3d-printing-in-education',
    excerpt: '探讨3D打印技术如何革新传统教育模式，为学生提供更加直观和互动的学习体验。',
    content: `
      <h2>3D打印技术简介</h2>
      <p>3D打印，又称增材制造，是一种通过逐层堆积材料来创建三维物体的技术。这项技术正在教育领域掀起一场革命。</p>
      
      <h2>教育应用的优势</h2>
      <h3>1. 可视化学习</h3>
      <p>复杂的概念可以通过3D模型直观展示，帮助学生更好地理解抽象知识。</p>
      
      <h3>2. 个性化教学</h3>
      <p>教师可以根据教学需要定制专门的教具和模型。</p>
      
      <h3>3. 激发创造力</h3>
      <p>学生可以将自己的想法转化为实体作品，极大地激发了创造热情。</p>
      
      <h2>具体应用场景</h2>
      <ul>
        <li><strong>数学课</strong>：打印几何体模型，帮助理解立体几何</li>
        <li><strong>历史课</strong>：复原历史文物，增强历史感知</li>
        <li><strong>生物课</strong>：制作器官模型，深入了解人体结构</li>
        <li><strong>艺术课</strong>：创作立体艺术作品，培养空间想象力</li>
      </ul>
      
      <h2>实施建议</h2>
      <p>1. <strong>设备选择</strong>：根据预算和需求选择合适的3D打印机</p>
      <p>2. <strong>软件培训</strong>：为师生提供3D建模软件的培训</p>
      <p>3. <strong>安全管理</strong>：建立完善的设备使用和安全管理制度</p>
      <p>4. <strong>课程整合</strong>：将3D打印技术有机融入各学科教学</p>
    `,
    tags: JSON.stringify(['3D打印', '技术教育', '创新教学', '数字化教育']),
    published: true,
    featured: false
  }
]

async function seedBlogPosts() {
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!adminUser) {
      console.log('未找到管理员用户，请先创建管理员账户')
      return
    }

    console.log('开始创建示例博客文章...')

    for (const postData of samplePosts) {
      const existingPost = await prisma.post.findUnique({
        where: { slug: postData.slug }
      })

      if (!existingPost) {
        await prisma.post.create({
          data: {
            ...postData,
            authorId: adminUser.id
          }
        })
        console.log(`✓ 创建文章: ${postData.title}`)
      } else {
        console.log(`- 文章已存在: ${postData.title}`)
      }
    }

    console.log('博客文章创建完成！')
  } catch (error) {
    console.error('创建博客文章失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedBlogPosts()
