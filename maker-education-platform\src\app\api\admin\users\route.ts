import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'
import bcrypt from 'bcryptjs'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '50'
    const role = searchParams.get('role')
    const status = searchParams.get('status')

    const pageSize = parseInt(limit)
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {}

    if (role && role !== 'ALL') {
      where.role = role
    }

    if (status === 'active') {
      where.isActive = true
    } else if (status === 'inactive') {
      where.isActive = false
    }

    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        avatar: true,
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize,
    })

    const total = await prisma.user.count({ where })

    return NextResponse.json(users)
  } catch (error) {
    console.error('Failed to fetch users:', error)
    return NextResponse.json(
      { error: '获取用户列表失败' },
      { status: 500 }
    )
  }
})

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const {
      email,
      username,
      name,
      password,
      role = 'USER',
      isActive = true
    } = await request.json()

    // 验证必填字段
    if (!email || !username || !name || !password) {
      return NextResponse.json(
        { error: '邮箱、用户名、姓名和密码为必填项' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUserByEmail) {
      return NextResponse.json(
        { error: '该邮箱已被使用' },
        { status: 400 }
      )
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUserByUsername) {
      return NextResponse.json(
        { error: '该用户名已被使用' },
        { status: 400 }
      )
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        username,
        name,
        password: hashedPassword,
        role,
        isActive
      },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        avatar: true,
      }
    })

    return NextResponse.json(user, { status: 201 })
  } catch (error) {
    console.error('Failed to create user:', error)
    return NextResponse.json(
      { error: '创建用户失败' },
      { status: 500 }
    )
  }
})
