@import "tailwindcss";

/* 基础样式 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: #1f2937;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 管理后台布局修复 */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-sidebar {
  flex-shrink: 0;
  width: 16rem; /* 64 * 0.25rem = 16rem */
}

.admin-main {
  flex: 1;
  min-width: 0;
}

/* 确保侧边栏和主内容区域顶部对齐 */
@media (min-width: 1024px) {
  .admin-layout {
    align-items: stretch;
  }

  .admin-sidebar {
    position: relative !important;
    transform: none !important;
    height: 100vh;
    display: flex !important;
    flex-direction: column !important;
  }

  .admin-main {
    padding-left: 0 !important;
  }
}

/* 文本截断 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 焦点样式 */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* 按钮悬停效果 */
.btn-hover {
  @apply transition-all duration-200 ease-in-out transform hover:scale-105;
}

/* 卡片悬停效果 */
.card-hover {
  @apply transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式容器 */
.container-responsive {
  @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite 2s;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite 4s;
}

/* 页面过渡动画 */
.page-transition {
  @apply transition-all duration-300 ease-in-out;
}

/* 卡片悬停动画增强 */
.card-hover-enhanced {
  @apply transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-2xl hover:-translate-y-2;
}

/* 按钮动画增强 */
.btn-enhanced {
  @apply transition-all duration-200 ease-in-out transform hover:scale-105 active:scale-95;
}

/* 导航栏动画 */
.nav-item {
  @apply relative transition-all duration-200 ease-in-out;
}

.nav-item::after {
  content: '';
  @apply absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 ease-in-out;
}

.nav-item:hover::after {
  @apply w-full;
}

/* 圆角增强 */
.rounded-enhanced {
  @apply rounded-2xl;
}

.rounded-card {
  @apply rounded-xl;
}

/* 阴影增强 */
.shadow-enhanced {
  box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 20px 25px -5px rgba(0, 0, 0, 0.04);
}

.shadow-hover {
  box-shadow: 0 20px 60px -10px rgba(0, 0, 0, 0.15), 0 25px 35px -5px rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500;
  resize: vertical;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white;
}

/* 移动端优化 */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4;
  }

  .mobile-text {
    @apply text-sm;
  }

  .mobile-hidden {
    @apply hidden;
  }
}

/* 平板优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid {
    @apply grid-cols-2;
  }
}

/* 桌面优化 */
@media (min-width: 1025px) {
  .desktop-grid {
    @apply grid-cols-3;
  }
}

/* 按钮增强动画 */
.btn-enhanced {
  @apply relative overflow-hidden;
  @apply focus:outline-none focus:ring-4 focus:ring-blue-500/20;
  @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
}

.btn-enhanced:active {
  @apply shadow-inner;
}

.btn-enhanced.loading {
  @apply animate-pulse cursor-wait;
}

/* 提交按钮特殊效果 */
.btn-submit {
  @apply relative overflow-hidden;
}

.btn-submit::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
  @apply transform -translate-x-full transition-transform duration-500;
}

.btn-submit:hover::before {
  @apply translate-x-full;
}

/* 输入框增强 */
.input-enhanced {
  @apply transition-all duration-200 ease-in-out;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply focus:shadow-lg focus:shadow-blue-500/10;
}

/* 错误状态 */
.input-error {
  @apply border-red-500 bg-red-50 focus:ring-red-500 focus:border-red-500;
  animation: shake 0.5s ease-in-out;
}

/* 成功状态 */
.input-success {
  @apply border-green-500 bg-green-50 focus:ring-green-500 focus:border-green-500;
}

/* 震动动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* 脉冲动画增强 */
@keyframes pulse-enhanced {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-enhanced {
  animation: pulse-enhanced 2s ease-in-out infinite;
}

/* 点击反馈 */
.click-feedback {
  @apply transition-all duration-150 ease-in-out;
}

.click-feedback:active {
  @apply transform scale-95 shadow-inner;
}
