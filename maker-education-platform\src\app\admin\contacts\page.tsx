'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { Mail, Phone, Calendar, MessageSquare, Eye, Trash2, CheckCircle } from 'lucide-react'

interface Contact {
  id: string
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  status: 'PENDING' | 'REPLIED' | 'CLOSED'
  createdAt: string
  updatedAt: string
}

export default function ContactsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [contacts, setContacts] = useState<Contact[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [filter, setFilter] = useState<'ALL' | 'PENDING' | 'REPLIED' | 'CLOSED'>('ALL')

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchContacts()
  }, [session, status, router])

  const fetchContacts = async () => {
    try {
      const response = await fetch('/api/admin/contacts')
      if (response.ok) {
        const data = await response.json()
        setContacts(data)
      }
    } catch (error) {
      console.error('Failed to fetch contacts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateContactStatus = async (id: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/contacts/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })

      if (response.ok) {
        fetchContacts()
        if (selectedContact?.id === id) {
          setSelectedContact({ ...selectedContact, status: status as any })
        }
      }
    } catch (error) {
      console.error('Failed to update contact status:', error)
    }
  }

  const deleteContact = async (id: string) => {
    if (!confirm('确定要删除这条联系信息吗？')) return

    try {
      const response = await fetch(`/api/admin/contacts/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        fetchContacts()
        if (selectedContact?.id === id) {
          setSelectedContact(null)
        }
      }
    } catch (error) {
      console.error('Failed to delete contact:', error)
    }
  }

  const filteredContacts = contacts.filter(contact => 
    filter === 'ALL' || contact.status === filter
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'REPLIED':
        return 'bg-green-100 text-green-800'
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return '待处理'
      case 'REPLIED':
        return '已回复'
      case 'CLOSED':
        return '已关闭'
      default:
        return status
    }
  }

  if (status === 'loading' || isLoading) {
    return <div>加载中...</div>
  }

  if (!session || session.user.role !== 'ADMIN') {
    return <div>无权限访问</div>
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">联系信息管理</h1>
            <p className="text-gray-600">管理用户提交的联系信息和咨询</p>
          </div>
        </div>

        {/* Filters */}
        <div className="flex space-x-4">
          {['ALL', 'PENDING', 'REPLIED', 'CLOSED'].map((status) => (
            <Button
              key={status}
              variant={filter === status ? 'default' : 'outline'}
              onClick={() => setFilter(status as any)}
              size="sm"
            >
              {status === 'ALL' ? '全部' : getStatusText(status)}
              {status !== 'ALL' && (
                <span className="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                  {contacts.filter(c => c.status === status).length}
                </span>
              )}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Contacts List */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-enhanced">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  联系信息列表 ({filteredContacts.length})
                </h2>
              </div>
              <div className="divide-y divide-gray-200">
                {filteredContacts.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <Mail className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <p>暂无联系信息</p>
                  </div>
                ) : (
                  filteredContacts.map((contact) => (
                    <div
                      key={contact.id}
                      className={`p-6 cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedContact?.id === contact.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                      }`}
                      onClick={() => setSelectedContact(contact)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-medium text-gray-900">{contact.name}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(contact.status)}`}>
                              {getStatusText(contact.status)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{contact.subject}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Mail className="w-4 h-4 mr-1" />
                              {contact.email}
                            </div>
                            {contact.phone && (
                              <div className="flex items-center">
                                <Phone className="w-4 h-4 mr-1" />
                                {contact.phone}
                              </div>
                            )}
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {new Date(contact.createdAt).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Contact Detail */}
          <div className="lg:col-span-1">
            {selectedContact ? (
              <div className="bg-white rounded-xl shadow-enhanced">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">联系详情</h2>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedContact.status)}`}>
                      {getStatusText(selectedContact.status)}
                    </span>
                  </div>
                </div>
                <div className="p-6 space-y-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">基本信息</h3>
                    <div className="space-y-2">
                      <p><span className="font-medium">姓名：</span>{selectedContact.name}</p>
                      <p><span className="font-medium">邮箱：</span>{selectedContact.email}</p>
                      {selectedContact.phone && (
                        <p><span className="font-medium">电话：</span>{selectedContact.phone}</p>
                      )}
                      <p><span className="font-medium">主题：</span>{selectedContact.subject}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">消息内容</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-gray-700 whitespace-pre-wrap">{selectedContact.message}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">时间信息</h3>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>提交时间：{new Date(selectedContact.createdAt).toLocaleString('zh-CN')}</p>
                      <p>更新时间：{new Date(selectedContact.updatedAt).toLocaleString('zh-CN')}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-gray-700">操作</h3>
                    <div className="space-y-2">
                      {selectedContact.status === 'PENDING' && (
                        <Button
                          onClick={() => updateContactStatus(selectedContact.id, 'REPLIED')}
                          className="w-full btn-enhanced"
                          size="sm"
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          标记为已回复
                        </Button>
                      )}
                      {selectedContact.status !== 'CLOSED' && (
                        <Button
                          onClick={() => updateContactStatus(selectedContact.id, 'CLOSED')}
                          variant="outline"
                          className="w-full"
                          size="sm"
                        >
                          关闭
                        </Button>
                      )}
                      <Button
                        onClick={() => deleteContact(selectedContact.id)}
                        variant="outline"
                        className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                        size="sm"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        删除
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-xl shadow-enhanced p-8 text-center">
                <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">选择一条联系信息查看详情</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
