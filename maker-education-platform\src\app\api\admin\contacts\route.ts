import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '50'

    const pageSize = parseInt(limit)
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {}

    if (status && status !== 'ALL') {
      where.status = status
    }

    const contacts = await prisma.contact.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize,
    })

    const total = await prisma.contact.count({ where })

    return NextResponse.json(contacts)
  } catch (error) {
    console.error('Failed to fetch contacts:', error)
    return NextResponse.json(
      { error: '获取联系信息失败' },
      { status: 500 }
    )
  }
})
