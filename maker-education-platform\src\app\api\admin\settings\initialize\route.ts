import { NextRequest, NextResponse } from 'next/server'
import { safeDbOperation } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

const defaultSettings = [
  // 基本信息
  { key: 'site_name', value: '创客教育平台', type: 'STRING', description: '网站名称', category: 'GENERAL' },
  { key: 'site_description', value: '专业的STEAM教育平台', type: 'STRING', description: '网站描述', category: 'GENERAL' },
  { key: 'site_slogan', value: '创新思维 · 实践能力', type: 'STRING', description: '网站标语', category: 'GENERAL' },
  { key: 'team_count', value: '50', type: 'NUMBER', description: '团队人数', category: 'GENERAL' },
  { key: 'copyright_year', value: '2024', type: 'STRING', description: '版权年份', category: 'GENERAL' },

  // 联系信息
  { key: 'contact_phone', value: '************', type: 'STRING', description: '联系电话', category: 'CONTACT' },
  { key: 'contact_email', value: '<EMAIL>', type: 'STRING', description: '联系邮箱', category: 'CONTACT' },
  { key: 'contact_address', value: '北京市海淀区创新大厦', type: 'STRING', description: '联系地址', category: 'CONTACT' },
  { key: 'contact_wechat', value: 'makeredu2024', type: 'STRING', description: '微信号', category: 'CONTACT' },
  { key: 'contact_qq', value: '123456789', type: 'STRING', description: 'QQ号', category: 'CONTACT' },
  { key: 'work_hours', value: '周一至周五 9:00-18:00', type: 'STRING', description: '工作时间', category: 'CONTACT' },

  // 功能开关
  { key: 'allow_registration', value: 'true', type: 'BOOLEAN', description: '允许注册', category: 'FEATURES' },
  { key: 'show_team_section', value: 'true', type: 'BOOLEAN', description: '显示团队介绍', category: 'FEATURES' },
  { key: 'show_projects_section', value: 'true', type: 'BOOLEAN', description: '显示作品展示', category: 'FEATURES' },
  { key: 'show_blog_section', value: 'true', type: 'BOOLEAN', description: '显示博客功能', category: 'FEATURES' },
  { key: 'show_apply_section', value: 'true', type: 'BOOLEAN', description: '显示申请功能', category: 'FEATURES' },
  { key: 'enable_comments', value: 'true', type: 'BOOLEAN', description: '启用评论功能', category: 'FEATURES' },
  { key: 'enable_likes', value: 'true', type: 'BOOLEAN', description: '启用点赞功能', category: 'FEATURES' },
  { key: 'maintenance_mode', value: 'false', type: 'BOOLEAN', description: '维护模式', category: 'FEATURES' },
  { key: 'require_approval', value: 'true', type: 'BOOLEAN', description: '申请需要审核', category: 'FEATURES' },

  // 外观设置
  { key: 'primary_color', value: '#3B82F6', type: 'STRING', description: '主色调', category: 'APPEARANCE' },
  { key: 'secondary_color', value: '#6366F1', type: 'STRING', description: '辅助色调', category: 'APPEARANCE' },
  { key: 'hero_background', value: 'gradient', type: 'STRING', description: '首页背景', category: 'APPEARANCE' },
  { key: 'show_animations', value: 'true', type: 'BOOLEAN', description: '显示动画效果', category: 'APPEARANCE' },
  { key: 'dark_mode_enabled', value: 'false', type: 'BOOLEAN', description: '启用深色模式', category: 'APPEARANCE' },

  // 首页内容
  { key: 'hero_title', value: '创新思维 · 实践能力', type: 'STRING', description: '首页标题', category: 'CONTENT' },
  { key: 'hero_subtitle', value: 'STEAM创客教育平台', type: 'STRING', description: '首页副标题', category: 'CONTENT' },
  { key: 'hero_description', value: '专注于STEAM教育和创客教育，通过3D打印、机器人制作、编程教学等创新课程，培养学生的创新思维和实践能力，为未来科技人才奠定坚实基础。', type: 'TEXT', description: '首页描述', category: 'CONTENT' },
  { key: 'about_intro', value: '我们是一支专业的STEAM教育团队，致力于为学生提供最优质的创客教育体验。', type: 'TEXT', description: '关于我们介绍', category: 'CONTENT' },

  // 统计数据
  { key: 'stats_projects', value: '500', type: 'NUMBER', description: '项目数量', category: 'STATS' },
  { key: 'stats_students', value: '1000', type: 'NUMBER', description: '学生数量', category: 'STATS' },
  { key: 'stats_satisfaction', value: '98', type: 'NUMBER', description: '满意度', category: 'STATS' },
  { key: 'total_members', value: '50', type: 'NUMBER', description: '总成员数', category: 'STATS' },
  { key: 'active_members', value: '35', type: 'NUMBER', description: '活跃成员数', category: 'STATS' },
  { key: 'total_projects', value: '120', type: 'NUMBER', description: '总项目数', category: 'STATS' },
  { key: 'completed_projects', value: '95', type: 'NUMBER', description: '完成项目数', category: 'STATS' },

  // SEO设置
  { key: 'meta_keywords', value: 'STEAM教育,创客教育,3D打印,机器人,编程', type: 'STRING', description: 'SEO关键词', category: 'SEO' },
  { key: 'meta_description', value: '专业的STEAM创客教育平台，提供3D打印、机器人制作、编程教学等创新课程', type: 'STRING', description: 'SEO描述', category: 'SEO' },

  // 法律文档
  { key: 'terms_content', value: '', type: 'TEXT', description: '使用条款内容', category: 'LEGAL' },
  { key: 'privacy_content', value: '', type: 'TEXT', description: '隐私政策内容', category: 'LEGAL' },
  { key: 'terms_last_updated', value: new Date().toISOString().split('T')[0], type: 'STRING', description: '使用条款更新日期', category: 'LEGAL' },
  { key: 'privacy_last_updated', value: new Date().toISOString().split('T')[0], type: 'STRING', description: '隐私政策更新日期', category: 'LEGAL' },

  // 自定义数据
  { key: 'facilities_equipment', value: '[]', type: 'JSON', description: '设施设备', category: 'CUSTOM' },
  { key: 'classroom_specs', value: '[]', type: 'JSON', description: '教室规格', category: 'CUSTOM' },
  { key: 'main_equipment', value: '[]', type: 'JSON', description: '主要设备', category: 'CUSTOM' }
]

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  const result = await safeDbOperation(async () => {
    const { prisma } = await import('@/lib/prisma')
    
    // 批量创建或更新默认设置
    for (const setting of defaultSettings) {
      await prisma.setting.upsert({
        where: { key: setting.key },
        update: {
          value: setting.value,
          type: setting.type,
          description: setting.description,
          category: setting.category,
          updatedAt: new Date()
        },
        create: setting
      })
    }
    
    return true
  }, false)

  if (result) {
    return NextResponse.json({ message: '默认设置初始化成功' })
  } else {
    return NextResponse.json(
      { error: '初始化设置失败' },
      { status: 500 }
    )
  }
})
