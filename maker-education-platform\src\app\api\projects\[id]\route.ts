import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const project = await prisma.project.findUnique({
      where: {
        id,
        status: 'PUBLISHED'
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        },
        comments: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    // 注意：浏览计数现在通过专门的 /view 端点处理

    // 解析JSON字符串为数组
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : []
    }

    return NextResponse.json(processedProject)
  } catch (error) {
    console.error('Failed to fetch project:', error)
    return NextResponse.json(
      { error: '获取作品详情失败' },
      { status: 500 }
    )
  }
}

export const PUT = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  try {
    const {
      title,
      description,
      content,
      images,
      category,
      tags,
      featured
    } = await request.json()

    // 检查作品是否存在且用户有权限编辑
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    if (existingProject.authorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无权限编辑此作品' },
        { status: 403 }
      )
    }

    const project = await prisma.project.update({
      where: { id },
      data: {
        title,
        description,
        content,
        images,
        category,
        tags,
        featured
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    return NextResponse.json(project)
  } catch (error) {
    console.error('Failed to update project:', error)
    return NextResponse.json(
      { error: '更新作品失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  try {
    // 检查作品是否存在且用户有权限删除
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { error: '作品未找到' },
        { status: 404 }
      )
    }

    if (existingProject.authorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无权限删除此作品' },
        { status: 403 }
      )
    }

    await prisma.project.delete({
      where: { id }
    })

    return NextResponse.json({ message: '作品删除成功' })
  } catch (error) {
    console.error('Failed to delete project:', error)
    return NextResponse.json(
      { error: '删除作品失败' },
      { status: 500 }
    )
  }
})
