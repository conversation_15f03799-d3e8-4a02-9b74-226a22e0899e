'use client'

import { useRef, useState } from 'react'
import { Download, Share2, Copy, Check } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ArticlePosterProps {
  title: string
  excerpt: string
  author: string
  publishDate: string
  readTime?: string
  category?: string
  siteUrl?: string
}

export function ArticlePoster({
  title,
  excerpt,
  author,
  publishDate,
  readTime = '5分钟阅读',
  category = '创客教育',
  siteUrl = 'makeredu.com'
}: ArticlePosterProps) {
  const posterRef = useRef<HTMLDivElement>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [showShareMenu, setShowShareMenu] = useState(false)
  const [copied, setCopied] = useState(false)

  const generatePoster = async () => {
    // 暂时简化，直接返回提示
    alert('海报生成功能正在开发中，敬请期待！')
    return null
  }

  const downloadPoster = async () => {
    const dataUrl = await generatePoster()
    if (dataUrl) {
      const link = document.createElement('a')
      link.download = `${title.slice(0, 20)}-海报.png`
      link.href = dataUrl
      link.click()
    }
  }

  const sharePoster = async (platform: string) => {
    if (platform === 'copy') {
      try {
        await navigator.clipboard.writeText(window.location.href)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (error) {
        console.error('复制失败:', error)
      }
    } else {
      downloadPoster()
    }
    setShowShareMenu(false)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-4">
      {/* 海报预览 */}
      <div 
        ref={posterRef}
        className="w-[400px] h-[500px] bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-2xl p-8 shadow-lg relative overflow-hidden mx-auto"
        style={{ fontFamily: 'system-ui, -apple-system, sans-serif' }}
      >
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-200 rounded-full opacity-20 transform -translate-x-12 translate-y-12"></div>
        
        {/* 内容 */}
        <div className="relative h-full flex flex-col">
          {/* 头部 */}
          <div className="text-center mb-6">
            <div className="inline-block bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
              {category}
            </div>
            <h1 className="text-2xl font-bold text-gray-900 leading-tight mb-4 line-clamp-3">
              {title}
            </h1>
          </div>

          {/* 摘要 */}
          <div className="flex-1 mb-6">
            <p className="text-gray-600 text-base leading-relaxed line-clamp-6">
              {excerpt}
            </p>
          </div>

          {/* 底部信息 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{formatDate(publishDate)}</span>
              <span>{readTime}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {author.charAt(0)}
                  </span>
                </div>
                <span className="text-gray-700 font-medium">{author}</span>
              </div>
              
              <div className="text-right">
                <div className="text-xs text-gray-400 mb-1">扫码阅读全文</div>
                <div className="w-12 h-12 bg-gray-900 rounded-lg flex items-center justify-center">
                  <div className="w-8 h-8 bg-white rounded grid grid-cols-3 gap-px">
                    {Array.from({ length: 9 }).map((_, i) => (
                      <div key={i} className="bg-gray-900 rounded-sm"></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* 网站信息 */}
            <div className="text-center pt-4 border-t border-gray-200">
              <div className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                创客教育平台
              </div>
              <div className="text-xs text-gray-400 mt-1">{siteUrl}</div>
            </div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={downloadPoster}
          disabled={isGenerating}
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>{isGenerating ? '生成中...' : '下载海报'}</span>
        </Button>

        <div className="relative">
          <Button
            variant="outline"
            onClick={() => setShowShareMenu(!showShareMenu)}
            className="flex items-center space-x-2"
          >
            <Share2 className="w-4 h-4" />
            <span>分享海报</span>
          </Button>

          {showShareMenu && (
            <>
              <div
                className="fixed inset-0 z-40"
                onClick={() => setShowShareMenu(false)}
              />
              <div className="absolute top-full mt-2 right-0 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-2 min-w-[160px]">
                <button
                  onClick={() => sharePoster('copy')}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm hover:bg-gray-50 rounded-md"
                >
                  {copied ? (
                    <Check className="w-4 h-4 text-green-500" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                  <span>{copied ? '已复制' : '复制图片'}</span>
                </button>
                <button
                  onClick={() => sharePoster('download')}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm hover:bg-gray-50 rounded-md"
                >
                  <Download className="w-4 h-4" />
                  <span>保存到相册</span>
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
