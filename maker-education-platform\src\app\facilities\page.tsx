'use client'

import { useState, useEffect } from 'react'
import { <PERSON>ch, CheckCircle, AlertTriangle, XCircle } from 'lucide-react'

interface Facility {
  id: string
  name: string
  description: string
  status: 'available' | 'maintenance' | 'unavailable'
}

export default function FacilitiesPage() {
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchFacilities()
  }, [])

  const fetchFacilities = async () => {
    try {
      const response = await fetch('/api/settings/public')
      if (response.ok) {
        const settings = await response.json()
        if (settings.facilities_equipment) {
          try {
            const facilitiesData = JSON.parse(settings.facilities_equipment)
            setFacilities(facilitiesData)
          } catch (e) {
            setFacilities([])
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch facilities:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'maintenance':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'unavailable':
        return <XCircle className="w-5 h-5 text-red-500" />
      default:
        return <CheckCircle className="w-5 h-5 text-green-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return '可用'
      case 'maintenance':
        return '维护中'
      case 'unavailable':
        return '不可用'
      default:
        return '可用'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-50 text-green-800 border-green-200'
      case 'maintenance':
        return 'bg-yellow-50 text-yellow-800 border-yellow-200'
      case 'unavailable':
        return 'bg-red-50 text-red-800 border-red-200'
      default:
        return 'bg-green-50 text-green-800 border-green-200'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center">
            <Wrench className="w-8 h-8 mr-3 text-blue-600" />
            设施设备
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            我们拥有先进的教学设施和专业设备，为学生提供最佳的学习环境
          </p>
        </div>

        {/* Facilities Grid */}
        {facilities.length === 0 ? (
          <div className="text-center py-12">
            <Wrench className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无设施设备信息</h3>
            <p className="text-gray-600">管理员还未添加设施设备信息</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {facilities.map((facility) => (
              <div
                key={facility.id}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {facility.name}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {facility.description || '暂无描述'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(facility.status)}`}>
                      {getStatusIcon(facility.status)}
                      <span className="ml-2">{getStatusText(facility.status)}</span>
                    </div>
                  </div>
                </div>

                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex items-center text-sm text-gray-500">
                    <Wrench className="w-4 h-4 mr-2" />
                    <span>专业设备</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Statistics */}
        {facilities.length > 0 && (
          <div className="mt-16 bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">设备统计</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <div className="text-3xl font-bold text-green-600 mb-1">
                  {facilities.filter(f => f.status === 'available').length}
                </div>
                <div className="text-gray-600">可用设备</div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <AlertTriangle className="w-8 h-8 text-yellow-600" />
                </div>
                <div className="text-3xl font-bold text-yellow-600 mb-1">
                  {facilities.filter(f => f.status === 'maintenance').length}
                </div>
                <div className="text-gray-600">维护中</div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Wrench className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-1">
                  {facilities.length}
                </div>
                <div className="text-gray-600">总设备数</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
