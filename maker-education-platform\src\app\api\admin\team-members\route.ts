import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const POST = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const {
      name,
      position,
      department,
      bio,
      skills,
      joinDate,
      displayOrder,
      isActive = true,
      userId
    } = await request.json()

    // 验证必填字段
    if (!name || !position) {
      return NextResponse.json(
        { error: '姓名和职位为必填项' },
        { status: 400 }
      )
    }

    // 如果没有指定显示顺序，使用当前最大值+1
    let finalDisplayOrder = displayOrder
    if (!finalDisplayOrder) {
      const maxOrder = await prisma.teamMember.aggregate({
        _max: {
          displayOrder: true
        }
      })
      finalDisplayOrder = (maxOrder._max.displayOrder || 0) + 1
    }

    const teamMember = await prisma.teamMember.create({
      data: {
        name,
        position,
        department: department || null,
        bio: bio || null,
        skills: JSON.stringify(skills || []),
        joinDate: joinDate ? new Date(joinDate) : new Date(),
        displayOrder: finalDisplayOrder,
        isActive,
        userId: userId || null
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedMember = {
      ...teamMember,
      skills: teamMember.skills ? JSON.parse(teamMember.skills) : []
    }

    return NextResponse.json(processedMember)
  } catch (error) {
    console.error('Failed to create team member:', error)
    return NextResponse.json(
      { error: '创建团队成员失败' },
      { status: 500 }
    )
  }
})
