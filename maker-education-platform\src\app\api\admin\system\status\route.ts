import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'
import os from 'os'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const startTime = Date.now()
    
    // 测试数据库连接
    let dbStatus = 'healthy'
    let dbResponseTime = 0
    try {
      const dbStart = Date.now()
      await prisma.$queryRaw`SELECT 1`
      dbResponseTime = Date.now() - dbStart
    } catch (error) {
      dbStatus = 'error'
      dbResponseTime = -1
    }

    // 获取系统信息
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      uptime: process.uptime(),
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usage: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(2)
      },
      cpu: {
        cores: os.cpus().length,
        model: os.cpus()[0]?.model || 'Unknown',
        load: os.loadavg()
      }
    }

    // 获取数据库统计
    const [
      totalUsers,
      totalProjects,
      totalPosts,
      totalApplications,
      dbSize
    ] = await Promise.all([
      prisma.user.count(),
      prisma.project.count(),
      prisma.post.count(),
      prisma.application.count(),
      // 估算数据库大小（SQLite特定）
      prisma.$queryRaw`SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()`.then(
        (result: any) => Number(result[0]?.size || 0)
      ).catch(() => 0)
    ])

    // 检查关键服务状态
    const services = [
      {
        name: 'Database',
        status: dbStatus,
        responseTime: dbResponseTime,
        details: `${totalUsers + totalProjects + totalPosts + totalApplications} records`
      },
      {
        name: 'Authentication',
        status: 'healthy',
        responseTime: 5,
        details: 'NextAuth.js running'
      },
      {
        name: 'File System',
        status: 'healthy',
        responseTime: 2,
        details: 'Read/Write access available'
      }
    ]

    // 计算总体健康状态
    const overallStatus = services.every(s => s.status === 'healthy') ? 'healthy' : 'warning'
    
    // API响应时间
    const apiResponseTime = Date.now() - startTime

    const systemStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: systemInfo.uptime,
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      
      // 性能指标
      performance: {
        apiResponseTime,
        dbResponseTime,
        memoryUsage: systemInfo.memory.usage,
        cpuCores: systemInfo.cpu.cores,
        loadAverage: systemInfo.cpu.load[0]
      },
      
      // 系统资源
      resources: {
        memory: {
          total: Math.round(systemInfo.memory.total / 1024 / 1024 / 1024 * 100) / 100, // GB
          used: Math.round(systemInfo.memory.used / 1024 / 1024 / 1024 * 100) / 100, // GB
          free: Math.round(systemInfo.memory.free / 1024 / 1024 / 1024 * 100) / 100, // GB
          usage: parseFloat(systemInfo.memory.usage)
        },
        cpu: {
          cores: systemInfo.cpu.cores,
          model: systemInfo.cpu.model,
          load: systemInfo.cpu.load
        },
        disk: {
          database: Math.round(Number(dbSize) / 1024 / 1024 * 100) / 100 // MB
        }
      },
      
      // 服务状态
      services,
      
      // 数据库统计
      database: {
        status: dbStatus,
        responseTime: dbResponseTime,
        records: {
          users: totalUsers,
          projects: totalProjects,
          posts: totalPosts,
          applications: totalApplications,
          total: totalUsers + totalProjects + totalPosts + totalApplications
        },
        size: Math.round(dbSize / 1024 / 1024 * 100) / 100 // MB
      },
      
      // 系统信息
      system: {
        platform: systemInfo.platform,
        architecture: systemInfo.arch,
        nodeVersion: systemInfo.nodeVersion,
        uptime: Math.round(systemInfo.uptime),
        startTime: new Date(Date.now() - systemInfo.uptime * 1000).toISOString()
      }
    }

    return NextResponse.json(systemStatus)
  } catch (error) {
    console.error('Failed to get system status:', error)
    return NextResponse.json(
      { 
        status: 'error',
        error: '获取系统状态失败',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
})
