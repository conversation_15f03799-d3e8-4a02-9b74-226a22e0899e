'use client'

import { useState, useEffect } from 'react'
import { Settings, Clock, Mail, Phone } from 'lucide-react'
import Link from 'next/link'

export default function MaintenancePage() {
  const [settings, setSettings] = useState<any>({})

  useEffect(() => {
    // 获取基本设置信息
    fetch('/api/settings/public')
      .then(res => res.json())
      .then(data => setSettings(data))
      .catch(err => console.error('Failed to fetch settings:', err))
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          {/* 维护图标 */}
          <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Settings className="w-10 h-10 text-orange-600 animate-spin" style={{ animationDuration: '3s' }} />
          </div>

          {/* 标题 */}
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            网站维护中
          </h1>

          {/* 描述 */}
          <p className="text-gray-600 mb-6 leading-relaxed">
            {settings.site_name || '创客教育平台'}正在进行系统维护和升级，
            为了给您提供更好的服务体验，我们暂时关闭了网站访问。
          </p>

          {/* 预计时间 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center text-gray-700 mb-2">
              <Clock className="w-5 h-5 mr-2" />
              <span className="font-medium">预计维护时间</span>
            </div>
            <p className="text-sm text-gray-600">
              我们正在努力尽快完成维护工作，请稍后再试
            </p>
          </div>

          {/* 联系信息 */}
          <div className="border-t pt-6">
            <p className="text-sm text-gray-600 mb-4">
              如有紧急事务，请通过以下方式联系我们：
            </p>
            
            <div className="space-y-2">
              {settings.contact_email && (
                <div className="flex items-center justify-center text-sm text-gray-700">
                  <Mail className="w-4 h-4 mr-2" />
                  <a 
                    href={`mailto:${settings.contact_email}`}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    {settings.contact_email}
                  </a>
                </div>
              )}
              
              {settings.contact_phone && (
                <div className="flex items-center justify-center text-sm text-gray-700">
                  <Phone className="w-4 h-4 mr-2" />
                  <a 
                    href={`tel:${settings.contact_phone}`}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    {settings.contact_phone}
                  </a>
                </div>
              )}
            </div>
          </div>

          {/* 管理员登录链接 */}
          <div className="mt-8 pt-6 border-t">
            <Link 
              href="/auth/signin"
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              管理员登录
            </Link>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            © {settings.copyright_year || new Date().getFullYear()} {settings.site_name || '创客教育平台'}. 保留所有权利.
          </p>
        </div>
      </div>
    </div>
  )
}
