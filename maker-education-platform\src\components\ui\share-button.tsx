'use client'

import { useState } from 'react'
import { Share2, Co<PERSON>, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ShareButtonProps {
  projectId: string
  projectTitle: string
  shareCount: number
  onShare: (platform: string) => void
  size?: 'sm' | 'md' | 'lg'
  showCount?: boolean
  className?: string
}

export function ShareButton({
  projectId,
  projectTitle,
  shareCount,
  onShare,
  size = 'md',
  showCount = true,
  className
}: ShareButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [copied, setCopied] = useState(false)

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-6 text-lg'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const shareUrl = `${window.location.origin}/projects/${projectId}`

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      onShare('copy')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const shareOptions = [
    {
      name: '微信',
      icon: '💬',
      action: () => {
        // 微信分享通常需要微信SDK，这里简化为复制链接
        copyToClipboard()
        onShare('wechat')
      }
    },
    {
      name: '微博',
      icon: '🐦',
      action: () => {
        const url = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(projectTitle)}`
        window.open(url, '_blank')
        onShare('weibo')
      }
    },
    {
      name: 'QQ',
      icon: '🐧',
      action: () => {
        const url = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(projectTitle)}`
        window.open(url, '_blank')
        onShare('qq')
      }
    },
    {
      name: '复制链接',
      icon: <Copy className="w-4 h-4" />,
      action: copyToClipboard
    }
  ]

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'inline-flex items-center gap-2 rounded-full border transition-all duration-200 font-medium',
          'bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300',
          'hover:scale-105 active:scale-95',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          sizeClasses[size],
          className
        )}
      >
        <Share2 className={cn(iconSizes[size], 'text-gray-400')} />
        {showCount && <span>{shareCount}</span>}
      </button>

      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* 分享弹窗 */}
          <div className="absolute top-full mt-2 right-0 z-50 bg-white rounded-xl shadow-lg border border-gray-200 p-4 min-w-[280px]">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-900">分享作品</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-4 h-4 text-gray-400" />
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              {shareOptions.map((option, index) => (
                <button
                  key={index}
                  onClick={() => {
                    option.action()
                    if (option.name !== '复制链接') {
                      setIsOpen(false)
                    }
                  }}
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <span className="text-lg">
                    {typeof option.icon === 'string' ? option.icon : option.icon}
                  </span>
                  <span className="text-sm text-gray-700">{option.name}</span>
                  {option.name === '复制链接' && copied && (
                    <Check className="w-4 h-4 text-green-500 ml-auto" />
                  )}
                </button>
              ))}
            </div>
            
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="flex-1 bg-transparent text-xs text-gray-600 outline-none"
                />
                <button
                  onClick={copyToClipboard}
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                >
                  {copied ? (
                    <Check className="w-4 h-4 text-green-500" />
                  ) : (
                    <Copy className="w-4 h-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
