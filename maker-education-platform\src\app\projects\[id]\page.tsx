'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ProjectInteraction } from '@/components/project/project-interaction'
import { ArrowLeft, Calendar, User, Tag } from 'lucide-react'

interface Project {
  id: string
  title: string
  description: string
  content: string
  images: string[]
  category: string
  tags: string[]
  viewCount: number
  likeCount: number
  shareCount: number
  createdAt: string
  author: {
    name: string
    avatar?: string
  }
}

const categories = {
  'STEAM_EDUCATION': 'STEAM教育',
  'THREE_D_PRINTING': '3D打印',
  'ROBOTICS': '机器人',
  'PROGRAMMING': '编程',
  'ELECTRONICS': '电子',
  'CRAFTS': '手工制作',
  'OTHER': '其他',
}

export default function ProjectDetail() {
  const params = useParams()
  const [project, setProject] = useState<Project | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  useEffect(() => {
    if (params.id) {
      fetchProject(params.id as string)
    }
  }, [params.id])

  const fetchProject = async (id: string) => {
    try {
      const response = await fetch(`/api/projects/${id}`)
      if (response.ok) {
        const data = await response.json()
        setProject(data)
      }
    } catch (error) {
      console.error('Failed to fetch project:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">作品未找到</h1>
          <Link href="/projects">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回作品列表
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link href="/projects">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回作品列表
            </Button>
          </Link>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Project Images */}
            {project.images.length > 0 && (
              <div className="mb-8">
                <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden mb-4">
                  <img
                    src={project.images[currentImageIndex]}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                {project.images.length > 1 && (
                  <div className="flex space-x-2 overflow-x-auto">
                    {project.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                          currentImageIndex === index ? 'border-blue-600' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={image}
                          alt={`${project.title} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Project Title and Description */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {project.title}
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                {project.description}
              </p>

              {/* Project Content */}
              {project.content && (
                <div className="prose max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: project.content }} />
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Info */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">作品信息</h3>
              
              {/* Author */}
              <div className="flex items-center mb-4">
                <User className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-500">作者</p>
                  <p className="font-medium text-gray-900">{project.author.name}</p>
                </div>
              </div>

              {/* Category */}
              <div className="flex items-center mb-4">
                <Tag className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-500">分类</p>
                  <p className="font-medium text-gray-900">
                    {categories[project.category as keyof typeof categories] || project.category}
                  </p>
                </div>
              </div>

              {/* Date */}
              <div className="flex items-center mb-4">
                <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-500">发布时间</p>
                  <p className="font-medium text-gray-900">{formatDate(project.createdAt)}</p>
                </div>
              </div>

              {/* Interactive Stats */}
              <div className="pt-4 border-t">
                <ProjectInteraction
                  projectId={project.id}
                  projectTitle={project.title}
                  size="sm"
                  layout="vertical"
                  className="gap-3"
                />
              </div>
            </div>

            {/* Tags */}
            {project.tags.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Interactive Actions */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">互动</h3>
              <ProjectInteraction
                projectId={project.id}
                projectTitle={project.title}
                size="md"
                layout="vertical"
                className="gap-4"
              />
            </div>

            {/* Related Projects */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">相关作品</h3>
              <p className="text-gray-500 text-sm">暂无相关作品</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
