import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const { platform } = await request.json()

    // 检查项目是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 更新分享计数
    const updatedProject = await prisma.project.update({
      where: { id: projectId },
      data: {
        shareCount: {
          increment: 1
        }
      }
    })

    return NextResponse.json({
      shareCount: updatedProject.shareCount,
      platform
    })
  } catch (error) {
    console.error('记录分享失败:', error)
    return NextResponse.json(
      { error: '操作失败，请稍后重试' },
      { status: 500 }
    )
  }
}
