'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { Calendar, Plus, Edit, Trash2, Clock, MapPin, User, Filter, Zap } from 'lucide-react'
import Link from 'next/link'

interface Schedule {
  id: string
  title: string
  description?: string
  startTime: string
  endTime: string
  location?: string
  type: string
  status: string
  color: string
  assignee?: {
    name: string
    avatar?: string
  }
  creator: {
    name: string
  }
}

const scheduleTypes = {
  'CLASS': { label: '课程', color: 'bg-blue-100 text-blue-800' },
  'MEETING': { label: '会议', color: 'bg-green-100 text-green-800' },
  'EVENT': { label: '活动', color: 'bg-purple-100 text-purple-800' },
  'MAINTENANCE': { label: '维护', color: 'bg-orange-100 text-orange-800' },
}

const scheduleStatus = {
  'ACTIVE': { label: '进行中', color: 'bg-green-100 text-green-800' },
  'CANCELLED': { label: '已取消', color: 'bg-red-100 text-red-800' },
  'COMPLETED': { label: '已完成', color: 'bg-gray-100 text-gray-800' },
}

export default function SchedulePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [filteredSchedules, setFilteredSchedules] = useState<Schedule[]>([])
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list')

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchSchedules()
  }, [session, status, router])

  useEffect(() => {
    filterSchedules()
  }, [schedules, typeFilter, statusFilter])

  const fetchSchedules = async () => {
    try {
      const response = await fetch('/api/admin/schedules')
      if (response.ok) {
        const data = await response.json()
        setSchedules(data)
      }
    } catch (error) {
      console.error('Failed to fetch schedules:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterSchedules = () => {
    let filtered = schedules

    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(schedule => schedule.type === typeFilter)
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(schedule => schedule.status === statusFilter)
    }

    setFilteredSchedules(filtered)
  }

  const deleteSchedule = async (scheduleId: string) => {
    if (!confirm('确定要删除这个排班吗？')) return

    try {
      const response = await fetch(`/api/admin/schedules/${scheduleId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchSchedules()
      }
    } catch (error) {
      console.error('Failed to delete schedule:', error)
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">排班管理</h2>
            <p className="text-gray-600">管理课程、会议和活动安排</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-xl"
            >
              列表视图
            </Button>
            <Button
              variant={viewMode === 'calendar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('calendar')}
              className="rounded-xl"
            >
              <Calendar className="w-4 h-4 mr-1" />
              日历视图
            </Button>
            <Link href="/admin/schedule/auto">
              <Button variant="outline" className="rounded-xl">
                <Zap className="w-4 h-4 mr-2" />
                自动排班
              </Button>
            </Link>
            <Button className="btn-enhanced rounded-xl">
              <Plus className="w-4 h-4 mr-2" />
              新建排班
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex gap-2 flex-wrap">
              <span className="text-sm font-medium text-gray-700 flex items-center">
                <Filter className="w-4 h-4 mr-1" />
                类型:
              </span>
              {['ALL', ...Object.keys(scheduleTypes)].map((type) => (
                <Button
                  key={type}
                  variant={typeFilter === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTypeFilter(type)}
                  className="rounded-xl"
                >
                  {type === 'ALL' ? '全部' : scheduleTypes[type as keyof typeof scheduleTypes].label}
                </Button>
              ))}
            </div>
            <div className="flex gap-2 flex-wrap">
              <span className="text-sm font-medium text-gray-700">状态:</span>
              {['ALL', ...Object.keys(scheduleStatus)].map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className="rounded-xl"
                >
                  {status === 'ALL' ? '全部' : scheduleStatus[status as keyof typeof scheduleStatus].label}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Schedule List */}
        {viewMode === 'list' && (
          <div className="bg-white rounded-xl shadow-enhanced overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      标题
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      负责人
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSchedules.map((schedule) => (
                    <tr key={schedule.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{schedule.title}</div>
                          {schedule.description && (
                            <div className="text-sm text-gray-500">{schedule.description}</div>
                          )}
                          {schedule.location && (
                            <div className="flex items-center text-sm text-gray-500 mt-1">
                              <MapPin className="w-3 h-3 mr-1" />
                              {schedule.location}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {formatDateTime(schedule.startTime)}
                          </div>
                          <div className="text-gray-500 ml-5">
                            至 {formatDateTime(schedule.endTime)}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          scheduleTypes[schedule.type as keyof typeof scheduleTypes]?.color || 'bg-gray-100 text-gray-800'
                        }`}>
                          {scheduleTypes[schedule.type as keyof typeof scheduleTypes]?.label || schedule.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          scheduleStatus[schedule.status as keyof typeof scheduleStatus]?.color || 'bg-gray-100 text-gray-800'
                        }`}>
                          {scheduleStatus[schedule.status as keyof typeof scheduleStatus]?.label || schedule.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {schedule.assignee ? (
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs font-medium">
                                {schedule.assignee.name.charAt(0)}
                              </span>
                            </div>
                            <span className="ml-2 text-sm text-gray-900">{schedule.assignee.name}</span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">未分配</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="rounded-lg"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteSchedule(schedule.id)}
                            className="rounded-lg text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredSchedules.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500">暂无排班数据</p>
              </div>
            )}
          </div>
        )}

        {/* Calendar View Placeholder */}
        {viewMode === 'calendar' && (
          <div className="bg-white rounded-xl shadow-enhanced p-8">
            <div className="text-center">
              <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">日历视图</h3>
              <p className="text-gray-500 mb-4">日历功能开发中...</p>
              <p className="text-sm text-gray-400">可集成 FullCalendar 或其他日历组件</p>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
