'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { ArrowLeft, CheckCircle, XCircle, User, Mail, Phone, Briefcase, Star, FileText, Calendar } from 'lucide-react'
import Link from 'next/link'

interface Application {
  id: string
  name: string
  email: string
  phone?: string
  position: string
  skills: string[]
  experience?: string
  motivation: string
  resume?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  reviewNote?: string
  reviewedBy?: string
  reviewedAt?: string
  createdAt: string
  applicant: {
    id: string
    name: string
    avatar?: string
  }
}

export default function ApplicationDetailPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [application, setApplication] = useState<Application | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [reviewNote, setReviewNote] = useState('')
  const [isReviewing, setIsReviewing] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    if (params.id) {
      fetchApplication()
    }
  }, [session, status, router, params.id])

  const fetchApplication = async () => {
    try {
      const response = await fetch(`/api/admin/applications/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setApplication(data)
        setReviewNote(data.reviewNote || '')
      } else {
        console.error('Failed to fetch application')
        router.push('/admin/applications')
      }
    } catch (error) {
      console.error('Failed to fetch application:', error)
      router.push('/admin/applications')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReview = async (status: 'APPROVED' | 'REJECTED') => {
    if (!application) return
    
    setIsReviewing(true)
    try {
      const response = await fetch(`/api/admin/applications/${application.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status,
          reviewNote: reviewNote.trim() || undefined
        })
      })

      if (response.ok) {
        const updatedApplication = await response.json()
        setApplication(updatedApplication)
        alert(`申请已${status === 'APPROVED' ? '通过' : '拒绝'}`)
      } else {
        alert('操作失败，请重试')
      }
    } catch (error) {
      console.error('Failed to review application:', error)
      alert('操作失败，请重试')
    } finally {
      setIsReviewing(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  if (!application) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">申请未找到</h2>
          <Link href="/admin/applications">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回申请列表
            </Button>
          </Link>
        </div>
      </AdminLayout>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'text-green-600 bg-green-50'
      case 'REJECTED': return 'text-red-600 bg-red-50'
      default: return 'text-yellow-600 bg-yellow-50'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'APPROVED': return '已通过'
      case 'REJECTED': return '已拒绝'
      default: return '待审核'
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/applications">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回列表
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">申请详情</h1>
              <p className="text-gray-600">查看和处理团队申请</p>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(application.status)}`}>
            {getStatusText(application.status)}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 申请信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 基本信息 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                基本信息
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                  <p className="text-gray-900">{application.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">申请职位</label>
                  <p className="text-gray-900">{application.position}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                  <p className="text-gray-900 flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    {application.email}
                  </p>
                </div>
                {application.phone && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">电话</label>
                    <p className="text-gray-900 flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      {application.phone}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* 技能和经验 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Star className="w-5 h-5 mr-2" />
                技能和经验
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">技能标签</label>
                  <div className="flex flex-wrap gap-2">
                    {application.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
                {application.experience && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">相关经验</label>
                    <p className="text-gray-900 whitespace-pre-wrap">{application.experience}</p>
                  </div>
                )}
              </div>
            </div>

            {/* 申请动机 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                申请动机
              </h3>
              <p className="text-gray-900 whitespace-pre-wrap">{application.motivation}</p>
            </div>

            {/* 简历 */}
            {application.resume && (
              <div className="bg-white rounded-xl shadow-enhanced p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Briefcase className="w-5 h-5 mr-2" />
                  简历
                </h3>
                <a
                  href={application.resume}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  查看简历文件
                </a>
              </div>
            )}
          </div>

          {/* 审核操作 */}
          <div className="space-y-6">
            {/* 申请时间 */}
            <div className="bg-white rounded-xl shadow-enhanced p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                申请时间
              </h3>
              <p className="text-gray-600">
                {new Date(application.createdAt).toLocaleString('zh-CN')}
              </p>
            </div>

            {/* 审核操作 */}
            {application.status === 'PENDING' && (
              <div className="bg-white rounded-xl shadow-enhanced p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">审核操作</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      审核备注（可选）
                    </label>
                    <textarea
                      value={reviewNote}
                      onChange={(e) => setReviewNote(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={3}
                      placeholder="请输入审核备注..."
                    />
                  </div>
                  <div className="flex space-x-3">
                    <Button
                      onClick={() => handleReview('APPROVED')}
                      disabled={isReviewing}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      通过
                    </Button>
                    <Button
                      onClick={() => handleReview('REJECTED')}
                      disabled={isReviewing}
                      variant="outline"
                      className="flex-1 text-red-600 border-red-600 hover:bg-red-50"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      拒绝
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* 审核结果 */}
            {application.status !== 'PENDING' && (
              <div className="bg-white rounded-xl shadow-enhanced p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">审核结果</h3>
                <div className="space-y-3">
                  <div className={`px-3 py-2 rounded-lg ${getStatusColor(application.status)}`}>
                    <div className="flex items-center">
                      {application.status === 'APPROVED' ? (
                        <CheckCircle className="w-4 h-4 mr-2" />
                      ) : (
                        <XCircle className="w-4 h-4 mr-2" />
                      )}
                      {getStatusText(application.status)}
                    </div>
                  </div>
                  {application.reviewNote && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">审核备注</label>
                      <p className="text-gray-900 text-sm">{application.reviewNote}</p>
                    </div>
                  )}
                  {application.reviewedAt && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">审核时间</label>
                      <p className="text-gray-600 text-sm">
                        {new Date(application.reviewedAt).toLocaleString('zh-CN')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
