'use client'

import { useState } from 'react'
import { Heart } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LikeButtonProps {
  liked: boolean
  likeCount: number
  onToggle: () => void
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  showCount?: boolean
  className?: string
}

export function LikeButton({
  liked,
  likeCount,
  onToggle,
  disabled = false,
  size = 'md',
  showCount = true,
  className
}: LikeButtonProps) {
  const [isAnimating, setIsAnimating] = useState(false)

  const handleClick = async () => {
    if (disabled) return

    setIsAnimating(true)
    await onToggle()
    
    // 动画持续时间
    setTimeout(() => setIsAnimating(false), 300)
  }

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-6 text-lg'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        'inline-flex items-center gap-2 rounded-full border transition-all duration-200 font-medium',
        'hover:scale-105 active:scale-95',
        'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
        sizeClasses[size],
        liked
          ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
          : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300',
        disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
        className
      )}
    >
      <Heart
        className={cn(
          iconSizes[size],
          'transition-all duration-200',
          liked ? 'fill-current text-red-500' : 'text-gray-400',
          isAnimating && 'animate-pulse scale-125'
        )}
      />
      {showCount && (
        <span className={cn(
          'transition-all duration-200',
          isAnimating && 'scale-110'
        )}>
          {likeCount}
        </span>
      )}
    </button>
  )
}
