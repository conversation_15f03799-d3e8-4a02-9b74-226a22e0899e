import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '10'

    const pageSize = parseInt(limit)
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {}
    if (status && status !== 'ALL') {
      where.status = status
    }

    const applications = await prisma.application.findMany({
      where,
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize,
    })

    // 解析JSON字符串为数组
    const processedApplications = applications.map(application => ({
      ...application,
      skills: application.skills ? JSON.parse(application.skills) : []
    }))

    const total = await prisma.application.count({ where })

    return NextResponse.json({
      applications: processedApplications,
      pagination: {
        page: parseInt(page),
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('Failed to fetch applications:', error)
    return NextResponse.json(
      { error: '获取申请列表失败' },
      { status: 500 }
    )
  }
})

export const POST = withAuth(async (request: NextRequest, session: any) => {
  try {
    const {
      name,
      email,
      phone,
      position,
      skills,
      experience,
      motivation,
      resume
    } = await request.json()

    // 验证必填字段
    if (!name || !email || !position || !motivation) {
      return NextResponse.json(
        { error: '姓名、邮箱、职位和申请动机为必填项' },
        { status: 400 }
      )
    }

    if (!skills || skills.length === 0) {
      return NextResponse.json(
        { error: '请至少选择一项技能' },
        { status: 400 }
      )
    }

    // 检查是否已有待审核的申请
    const existingApplication = await prisma.application.findFirst({
      where: {
        applicantId: session.user.id,
        status: 'PENDING'
      }
    })

    if (existingApplication) {
      return NextResponse.json(
        { error: '您已有待审核的申请，请等待审核结果' },
        { status: 400 }
      )
    }

    const application = await prisma.application.create({
      data: {
        applicantId: session.user.id,
        name,
        email,
        phone,
        position,
        skills: JSON.stringify(skills),
        experience,
        motivation,
        resume,
        status: 'PENDING'
      },
      include: {
        applicant: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedApplication = {
      ...application,
      skills: application.skills ? JSON.parse(application.skills) : []
    }

    return NextResponse.json(processedApplication, { status: 201 })
  } catch (error) {
    console.error('Failed to create application:', error)
    return NextResponse.json(
      { error: '提交申请失败' },
      { status: 500 }
    )
  }
})
