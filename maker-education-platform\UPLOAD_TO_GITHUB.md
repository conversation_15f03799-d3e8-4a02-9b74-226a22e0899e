# 📦 GitHub上传清单

## 需要上传的文件和文件夹

### ✅ 必须上传的文件夹：
- `src/` - 源代码文件夹
- `prisma/` - 数据库配置
- `scripts/` - 部署脚本
- `public/` - 静态资源

### ✅ 必须上传的配置文件：
- `package.json` - 项目依赖
- `package-lock.json` - 锁定依赖版本
- `next.config.js` - Next.js配置
- `tailwind.config.ts` - 样式配置
- `tsconfig.json` - TypeScript配置
- `vercel.json` - Vercel部署配置
- `.env.example` - 环境变量示例
- `.gitignore` - Git忽略文件
- `README.md` - 项目说明
- `deploy.md` - 部署指南

### ❌ 不要上传的文件夹：
- `node_modules/` - 依赖包（会自动安装）
- `.next/` - 构建输出
- `.env` - 本地环境变量（包含敏感信息）
- `prisma/dev.db` - 本地数据库文件

## 🚀 快速上传步骤

### 方法一：拖拽上传（推荐）
1. 在GitHub创建新仓库
2. 选择所有需要的文件和文件夹
3. 拖拽到GitHub页面
4. 提交更改

### 方法二：逐个上传
1. 先上传配置文件
2. 再上传src文件夹
3. 最后上传其他文件夹

## 📋 上传后检查清单

上传完成后，确保GitHub仓库包含：
- [ ] src/app/ 文件夹
- [ ] src/components/ 文件夹  
- [ ] src/lib/ 文件夹
- [ ] prisma/schema.prisma 文件
- [ ] package.json 文件
- [ ] next.config.js 文件
- [ ] vercel.json 文件

## 🔗 下一步

上传完成后，按照 `deploy.md` 文件中的步骤进行部署。
