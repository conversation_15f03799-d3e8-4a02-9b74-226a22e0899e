'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { Search, Plus, Edit, Trash2, User, MapPin } from 'lucide-react'

interface TeamMember {
  id: string
  name: string
  position: string
  department?: string
  avatar?: string
  bio?: string
  skills: string[]
  joinDate: string
  isActive: boolean
  displayOrder: number
}

export default function TeamMembersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [filteredMembers, setFilteredMembers] = useState<TeamMember[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    fetchTeamMembers()
  }, [session, status, router])

  useEffect(() => {
    filterMembers()
  }, [teamMembers, searchTerm])

  const fetchTeamMembers = async () => {
    try {
      const response = await fetch('/api/team-members?includeInactive=true')
      if (response.ok) {
        const data = await response.json()
        setTeamMembers(data)
      }
    } catch (error) {
      console.error('Failed to fetch team members:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterMembers = () => {
    let filtered = teamMembers

    if (searchTerm) {
      filtered = filtered.filter(member =>
        member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.department?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredMembers(filtered)
  }

  const toggleMemberStatus = async (memberId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/team-members/${memberId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !isActive })
      })

      if (response.ok) {
        fetchTeamMembers()
      }
    } catch (error) {
      console.error('Failed to update member status:', error)
    }
  }

  const deleteMember = async (memberId: string) => {
    if (!confirm('确定要删除这个团队成员吗？')) return

    try {
      const response = await fetch(`/api/admin/team-members/${memberId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchTeamMembers()
      }
    } catch (error) {
      console.error('Failed to delete member:', error)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </AdminLayout>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">团队管理</h2>
            <p className="text-gray-600">管理团队成员信息</p>
          </div>
          <Button
            onClick={() => router.push('/admin/team-members/new')}
            className="btn-enhanced rounded-xl"
          >
            <Plus className="w-4 h-4 mr-2" />
            添加成员
          </Button>
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="搜索团队成员..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <div key={member.id} className="bg-white rounded-xl shadow-enhanced p-6 card-hover">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-lg font-medium">
                      {member.name.charAt(0)}
                    </span>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-semibold text-gray-900">{member.name}</h3>
                    <p className="text-sm text-blue-600">{member.position}</p>
                  </div>
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  member.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {member.isActive ? '活跃' : '禁用'}
                </span>
              </div>

              {member.department && (
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <MapPin className="w-4 h-4 mr-1" />
                  {member.department}
                </div>
              )}

              {member.bio && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                  {member.bio}
                </p>
              )}

              <div className="flex flex-wrap gap-1 mb-4">
                {member.skills.slice(0, 3).map((skill, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs"
                  >
                    {skill}
                  </span>
                ))}
                {member.skills.length > 3 && (
                  <span className="text-gray-400 text-xs px-2 py-1">
                    +{member.skills.length - 3}
                  </span>
                )}
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>加入于 {new Date(member.joinDate).toLocaleDateString('zh-CN')}</span>
                <span>排序: {member.displayOrder}</span>
              </div>

              <div className="flex justify-between items-center">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => toggleMemberStatus(member.id, member.isActive)}
                  className="rounded-lg"
                >
                  {member.isActive ? '禁用' : '启用'}
                </Button>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => router.push(`/admin/team-members/${member.id}/edit`)}
                    className="rounded-lg"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteMember(member.id)}
                    className="rounded-lg text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无团队成员</p>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
