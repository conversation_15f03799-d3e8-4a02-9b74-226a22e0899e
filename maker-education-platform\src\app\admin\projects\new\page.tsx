'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Save, FileText, Tag, Star, Image } from 'lucide-react'
import Link from 'next/link'

const categories = [
  { value: 'STEAM_EDUCATION', label: 'STEAM教育' },
  { value: 'THREE_D_PRINTING', label: '3D打印' },
  { value: 'ROBOTICS', label: '机器人' },
  { value: 'PROGRAMMING', label: '编程' },
  { value: 'ELECTRONICS', label: '电子' },
  { value: 'CRAFTS', label: '手工制作' },
  { value: 'OTHER', label: '其他' },
]

const tagOptions = [
  '3D打印', '机器人', 'Arduino', 'Scratch', '编程', '创新', '设计',
  '环保', '智能', '传感器', '物联网', '人工智能', '创意', '实用'
]

// 确保分类值是枚举值的函数
const ensureCategoryEnum = (value: string) => {
  // 如果已经是枚举值，直接返回
  if (categories.some(cat => cat.value === value)) {
    return value
  }
  // 如果是中文标签，转换为枚举值
  const category = categories.find(cat => cat.label === value)
  return category ? category.value : value
}

export default function NewProject() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    images: [] as string[],
    tags: [] as string[],
    featured: false,
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    })
  }

  const handleTagToggle = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }))
  }

  const handleImageAdd = () => {
    const url = prompt('请输入图片URL:')
    if (url) {
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, url]
      }))
    }
  }

  const handleImageRemove = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    if (!formData.title || !formData.description || !formData.category) {
      setError('标题、描述和分类为必填项')
      setIsLoading(false)
      return
    }

    // 确保分类值是正确的枚举值
    const submitData = {
      ...formData,
      category: ensureCategoryEnum(formData.category)
    }



    try {
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        router.push('/admin/projects')
      } else {
        const data = await response.json()
        setError(data.error || '创建失败，请稍后重试')
      }
    } catch (error) {
      setError('创建失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/projects">
              <Button variant="outline" size="sm" className="rounded-xl">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回
              </Button>
            </Link>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">添加作品</h2>
              <p className="text-gray-600">创建新的学生作品展示</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                基本信息
              </h3>
              <div className="space-y-4">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                    作品标题 *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.title}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                    作品分类 *
                  </label>
                  <select
                    id="category"
                    name="category"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.category}
                    onChange={handleChange}
                  >
                    <option value="">请选择分类</option>
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    作品描述 *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入作品的简短描述..."
                    value={formData.description}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                    详细内容
                  </label>
                  <textarea
                    id="content"
                    name="content"
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入作品的详细介绍，支持HTML格式..."
                    value={formData.content}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            {/* Images */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Image className="w-5 h-5 mr-2" />
                作品图片
              </h3>
              <div className="space-y-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleImageAdd}
                  className="rounded-xl"
                >
                  <Image className="w-4 h-4 mr-2" />
                  添加图片
                </Button>

                {formData.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={image}
                          alt={`作品图片 ${index + 1}`}
                          className="w-full h-32 object-cover rounded-xl border"
                        />
                        <button
                          type="button"
                          onClick={() => handleImageRemove(index)}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Tag className="w-5 h-5 mr-2" />
                标签
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {tagOptions.map((tag) => (
                  <label key={tag} className="flex items-center">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      checked={formData.tags.includes(tag)}
                      onChange={() => handleTagToggle(tag)}
                    />
                    <span className="ml-2 text-sm text-gray-700">{tag}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Settings */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Star className="w-5 h-5 mr-2" />
                设置
              </h3>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  checked={formData.featured}
                  onChange={handleChange}
                />
                <label htmlFor="featured" className="ml-2 text-sm text-gray-700">
                  设为精选作品
                </label>
              </div>
            </div>

            {error && (
              <div className="text-red-600 text-sm bg-red-50 p-3 rounded-xl border border-red-200">
                {error}
              </div>
            )}

            <div className="flex justify-end space-x-4 pt-6 border-t">
              <Link href="/admin/projects">
                <Button type="button" variant="outline" className="rounded-xl">
                  取消
                </Button>
              </Link>
              <Button type="submit" disabled={isLoading} className="rounded-xl">
                <Save className="w-4 h-4 mr-2" />
                {isLoading ? '保存中...' : '保存'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  )
}
