import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'
    
    // 计算时间范围
    const now = new Date()
    let startDate: Date
    
    switch (timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    }

    // 获取基础统计数据
    const [
      totalUsers,
      totalProjects,
      totalPosts,
      totalApplications,
      totalViews,
      userGrowth,
      projectGrowth,
      postGrowth,
      popularProjects,
      popularPosts,
      recentActivity
    ] = await Promise.all([
      // 总用户数
      prisma.user.count({
        where: { isActive: true }
      }),
      
      // 总项目数
      prisma.project.count({
        where: { status: 'PUBLISHED' }
      }),
      
      // 总文章数
      prisma.post.count({
        where: { published: true }
      }),
      
      // 总申请数
      prisma.application.count(),
      
      // 总浏览量（项目浏览量之和）
      prisma.project.aggregate({
        _sum: {
          viewCount: true
        },
        where: { status: 'PUBLISHED' }
      }).then(result => result._sum.viewCount || 0),
      
      // 用户增长（时间范围内新增用户）
      prisma.user.count({
        where: {
          createdAt: {
            gte: startDate
          },
          isActive: true
        }
      }),
      
      // 项目增长（时间范围内新增项目）
      prisma.project.count({
        where: {
          createdAt: {
            gte: startDate
          },
          status: 'PUBLISHED'
        }
      }),
      
      // 文章增长（时间范围内新增文章）
      prisma.post.count({
        where: {
          createdAt: {
            gte: startDate
          },
          published: true
        }
      }),
      
      // 热门项目（按浏览量排序）
      prisma.project.findMany({
        where: { status: 'PUBLISHED' },
        select: {
          id: true,
          title: true,
          viewCount: true,
          category: true,
          createdAt: true
        },
        orderBy: {
          viewCount: 'desc'
        },
        take: 10
      }),
      
      // 热门文章（按浏览量排序）
      prisma.post.findMany({
        where: { published: true },
        select: {
          id: true,
          title: true,
          viewCount: true,
          createdAt: true,
          author: {
            select: {
              name: true
            }
          }
        },
        orderBy: {
          viewCount: 'desc'
        },
        take: 10
      }),
      
      // 最近活动（最近的用户注册、项目发布、文章发布等）
      Promise.all([
        // 最近用户注册
        prisma.user.findMany({
          where: {
            createdAt: {
              gte: startDate
            },
            isActive: true
          },
          select: {
            name: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        }).then(users => users.map(user => ({
          type: 'user_registration',
          description: `新用户 ${user.name} 注册`,
          timestamp: user.createdAt.toISOString()
        }))),
        
        // 最近项目发布
        prisma.project.findMany({
          where: {
            createdAt: {
              gte: startDate
            },
            status: 'PUBLISHED'
          },
          select: {
            title: true,
            createdAt: true,
            author: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        }).then(projects => projects.map(project => ({
          type: 'project_published',
          description: `${project.author.name} 发布了项目 "${project.title}"`,
          timestamp: project.createdAt.toISOString()
        }))),
        
        // 最近文章发布
        prisma.post.findMany({
          where: {
            createdAt: {
              gte: startDate
            },
            published: true
          },
          select: {
            title: true,
            createdAt: true,
            author: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        }).then(posts => posts.map(post => ({
          type: 'post_published',
          description: `${post.author.name} 发布了文章 "${post.title}"`,
          timestamp: post.createdAt.toISOString()
        })))
      ]).then(activities => {
        // 合并所有活动并按时间排序
        const allActivities = activities.flat()
        return allActivities.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        ).slice(0, 20)
      })
    ])

    // 计算增长率
    const calculateGrowthRate = (current: number, growth: number) => {
      if (current === 0) return 0
      const previous = current - growth
      if (previous === 0) return growth > 0 ? 100 : 0
      return Math.round((growth / previous) * 100)
    }

    const analyticsData = {
      totalUsers,
      totalProjects,
      totalPosts,
      totalApplications,
      totalViews,
      userGrowth: calculateGrowthRate(totalUsers, userGrowth),
      projectGrowth: calculateGrowthRate(totalProjects, projectGrowth),
      postGrowth: calculateGrowthRate(totalPosts, postGrowth),
      popularProjects,
      popularPosts,
      recentActivity,
      timeRange,
      // 额外的统计信息
      stats: {
        avgProjectViews: totalProjects > 0 ? Math.round(totalViews / totalProjects) : 0,
        activeUsersToday: userGrowth, // 今日新增用户作为活跃用户的近似值
        publishedContentToday: projectGrowth + postGrowth
      }
    }

    return NextResponse.json(analyticsData)
  } catch (error) {
    console.error('Failed to fetch analytics:', error)
    return NextResponse.json(
      { error: '获取分析数据失败' },
      { status: 500 }
    )
  }
})
