import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')
    const page = searchParams.get('page') || '1'

    const pageSize = limit ? parseInt(limit) : 12
    const skip = (parseInt(page) - 1) * pageSize

    const where: any = {
      status: 'PUBLISHED'
    }

    if (category && category !== 'ALL') {
      where.category = category
    }

    if (featured === 'true') {
      where.featured = true
    }

    const projects = await prisma.project.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize,
    })

    // 解析JSON字符串为数组
    const processedProjects = projects.map(project => ({
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : []
    }))

    const total = await prisma.project.count({ where })

    return NextResponse.json({
      projects: processedProjects,
      pagination: {
        page: parseInt(page),
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('Failed to fetch projects:', error)
    return NextResponse.json(
      { error: '获取作品列表失败' },
      { status: 500 }
    )
  }
}

export const POST = withAuth(async (request: NextRequest, session: any) => {
  try {
    const {
      title,
      description,
      content,
      images,
      category,
      tags,
      featured = false
    } = await request.json()

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: '标题、描述和分类为必填项' },
        { status: 400 }
      )
    }

    const project = await prisma.project.create({
      data: {
        title,
        description,
        content,
        images: JSON.stringify(images || []),
        category,
        tags: JSON.stringify(tags || []),
        featured,
        authorId: session.user.id,
        status: 'PUBLISHED'
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedProject = {
      ...project,
      images: project.images ? JSON.parse(project.images) : [],
      tags: project.tags ? JSON.parse(project.tags) : []
    }

    return NextResponse.json(processedProject, { status: 201 })
  } catch (error) {
    console.error('Failed to create project:', error)
    return NextResponse.json(
      { error: '创建作品失败' },
      { status: 500 }
    )
  }
})
