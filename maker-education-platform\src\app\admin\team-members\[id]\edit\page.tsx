'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import AdminLayout from '@/components/layout/admin-layout'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Save, User } from 'lucide-react'
import Link from 'next/link'

interface TeamMember {
  id: string
  name: string
  position: string
  bio: string
  email: string
  phone: string
  avatar: string
  isActive: boolean
  joinDate: string
  user?: {
    id: string
    email: string
    role: string
  }
}

export default function EditTeamMemberPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [member, setMember] = useState<TeamMember | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    bio: '',
    email: '',
    phone: '',
    avatar: '',
    isActive: true
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
    if (params.id) {
      fetchMember()
    }
  }, [session, status, router, params.id])

  const fetchMember = async () => {
    try {
      const response = await fetch(`/api/admin/team-members/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setMember(data)
        setFormData({
          name: data.name || '',
          position: data.position || '',
          bio: data.bio || '',
          email: data.email || '',
          phone: data.phone || '',
          avatar: data.avatar || '',
          isActive: data.isActive
        })
      } else {
        setMessage('团队成员不存在')
      }
    } catch (error) {
      console.error('Failed to fetch member:', error)
      setMessage('获取团队成员信息失败')
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      const response = await fetch(`/api/admin/team-members/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        setMessage('团队成员更新成功！')
        setTimeout(() => {
          router.push('/admin/team-members')
        }, 1500)
      } else {
        const error = await response.json()
        setMessage(error.message || '更新失败')
      }
    } catch (error) {
      console.error('Failed to update member:', error)
      setMessage('更新失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (status === 'loading') {
    return <div>加载中...</div>
  }

  if (!session || session.user.role !== 'ADMIN') {
    return <div>无权限访问</div>
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/team-members">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">编辑团队成员</h1>
              <p className="text-gray-600">修改团队成员信息</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-xl shadow-enhanced p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  姓名 *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  职位 *
                </label>
                <input
                  type="text"
                  name="position"
                  value={formData.position}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  电话
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  头像URL
                </label>
                <input
                  type="url"
                  name="avatar"
                  value={formData.avatar}
                  onChange={handleChange}
                  placeholder="https://example.com/avatar.jpg"
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  个人简介
                </label>
                <textarea
                  name="bio"
                  value={formData.bio}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="ml-2 text-sm text-gray-700">
                    激活状态
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Link href="/admin/team-members">
                <Button type="button" variant="outline">
                  取消
                </Button>
              </Link>
              <Button
                type="submit"
                disabled={isLoading}
                className="btn-enhanced btn-submit"
              >
                <Save className="w-4 h-4 mr-2" />
                {isLoading ? '保存中...' : '保存修改'}
              </Button>
            </div>

            {message && (
              <div className={`p-4 rounded-xl ${
                message.includes('成功') 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {message}
              </div>
            )}
          </form>
        </div>
      </div>
    </AdminLayout>
  )
}
