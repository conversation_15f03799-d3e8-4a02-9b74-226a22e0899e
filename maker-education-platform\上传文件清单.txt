📦 GitHub上传文件清单

✅ 必须上传的文件夹：
📁 src/
   📁 app/
   📁 components/
   📁 lib/
   📁 types/

📁 prisma/
   📄 schema.prisma
   📄 seed.ts

📁 scripts/
   📄 init-db.js

📁 public/
   📄 favicon.ico
   📁 images/ (如果有)

✅ 必须上传的根目录文件：
📄 package.json
📄 package-lock.json
📄 next.config.js
📄 tailwind.config.ts
📄 tsconfig.json
📄 vercel.json
📄 .env.example
📄 .gitignore
📄 README.md
📄 deploy.md
📄 UPLOAD_TO_GITHUB.md
📄 netlify.toml
📄 railway.json

❌ 不要上传的文件夹：
📁 node_modules/ (太大，会自动安装)
📁 .next/ (构建输出)
📄 .env (包含敏感信息)
📄 prisma/dev.db (本地数据库)

总计文件大小：约 5-10MB (不包含node_modules)
