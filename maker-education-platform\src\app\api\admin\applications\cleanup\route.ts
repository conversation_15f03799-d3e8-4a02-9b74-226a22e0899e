import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const DELETE = withAdmin(async (request: NextRequest, session: any) => {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // 'APPROVED', 'REJECTED', 'ALL'
    
    let whereCondition: any = {}
    
    if (status === 'APPROVED') {
      whereCondition.status = 'APPROVED'
    } else if (status === 'REJECTED') {
      whereCondition.status = 'REJECTED'
    } else if (status === 'ALL') {
      whereCondition.status = {
        in: ['APPROVED', 'REJECTED']
      }
    } else {
      return NextResponse.json(
        { error: '无效的状态参数' },
        { status: 400 }
      )
    }

    // 删除指定状态的申请
    const deletedApplications = await prisma.application.deleteMany({
      where: whereCondition
    })

    return NextResponse.json({
      message: `成功清理 ${deletedApplications.count} 条申请记录`,
      deletedCount: deletedApplications.count
    })
  } catch (error) {
    console.error('Failed to cleanup applications:', error)
    return NextResponse.json(
      { error: '清理申请失败' },
      { status: 500 }
    )
  }
})
