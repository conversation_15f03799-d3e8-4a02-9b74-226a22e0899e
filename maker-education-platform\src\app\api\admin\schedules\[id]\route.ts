import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const schedule = await prisma.schedule.findUnique({
      where: { id: params.id },
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    })

    if (!schedule) {
      return NextResponse.json(
        { error: '排班未找到' },
        { status: 404 }
      )
    }

    return NextResponse.json(schedule)
  } catch (error) {
    console.error('Failed to fetch schedule:', error)
    return NextResponse.json(
      { error: '获取排班信息失败' },
      { status: 500 }
    )
  }
})

export const PATCH = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const {
      title,
      description,
      startTime,
      endTime,
      location,
      type,
      status,
      assignedTo,
      color
    } = await request.json()

    const updateData: any = {}
    if (title !== undefined) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (startTime !== undefined) updateData.startTime = new Date(startTime)
    if (endTime !== undefined) updateData.endTime = new Date(endTime)
    if (location !== undefined) updateData.location = location
    if (type !== undefined) updateData.type = type
    if (status !== undefined) updateData.status = status
    if (assignedTo !== undefined) updateData.assignedTo = assignedTo
    if (color !== undefined) updateData.color = color

    // 如果更新时间，检查冲突
    if (startTime || endTime || assignedTo) {
      const currentSchedule = await prisma.schedule.findUnique({
        where: { id: params.id }
      })

      if (!currentSchedule) {
        return NextResponse.json(
          { error: '排班未找到' },
          { status: 404 }
        )
      }

      const checkStartTime = startTime ? new Date(startTime) : currentSchedule.startTime
      const checkEndTime = endTime ? new Date(endTime) : currentSchedule.endTime
      const checkAssignedTo = assignedTo !== undefined ? assignedTo : currentSchedule.assignedTo

      if (checkAssignedTo) {
        const conflictingSchedule = await prisma.schedule.findFirst({
          where: {
            id: { not: params.id },
            assignedTo: checkAssignedTo,
            status: 'ACTIVE',
            OR: [
              {
                startTime: { lte: checkStartTime },
                endTime: { gt: checkStartTime }
              },
              {
                startTime: { lt: checkEndTime },
                endTime: { gte: checkEndTime }
              }
            ]
          }
        })

        if (conflictingSchedule) {
          return NextResponse.json(
            { error: '该时间段已有其他安排' },
            { status: 400 }
          )
        }
      }
    }

    const schedule = await prisma.schedule.update({
      where: { id: params.id },
      data: updateData,
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    })

    return NextResponse.json(schedule)
  } catch (error) {
    console.error('Failed to update schedule:', error)
    return NextResponse.json(
      { error: '更新排班失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    await prisma.schedule.delete({
      where: { id }
    })

    return NextResponse.json({ message: '排班删除成功' })
  } catch (error) {
    console.error('Failed to delete schedule:', error)
    return NextResponse.json(
      { error: '删除排班失败' },
      { status: 500 }
    )
  }
})
