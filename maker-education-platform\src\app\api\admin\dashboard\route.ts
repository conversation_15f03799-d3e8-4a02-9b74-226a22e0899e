import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (request: NextRequest, session: any) => {
  try {
    // 获取统计数据
    const [
      totalUsers,
      totalProjects,
      pendingApplications,
      totalTeamMembers,
      totalPosts,
      publishedPosts,
      recentProjects,
      recentApplications,
      recentPosts,
      todayStats
    ] = await Promise.all([
      // 总用户数
      prisma.user.count({
        where: { isActive: true }
      }),
      
      // 总作品数
      prisma.project.count({
        where: { status: 'PUBLISHED' }
      }),
      
      // 待审核申请数
      prisma.application.count({
        where: { status: 'PENDING' }
      }),
      
      // 团队成员数
      prisma.teamMember.count({
        where: { isActive: true }
      }),

      // 总文章数
      prisma.post.count(),

      // 已发布文章数
      prisma.post.count({
        where: { published: true }
      }),
      
      // 最新作品
      prisma.project.findMany({
        where: { status: 'PUBLISHED' },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),
      
      // 最新申请
      prisma.application.findMany({
        include: {
          applicant: {
            select: {
              id: true,
              name: true,
              avatar: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      }),

      // 最新文章
      prisma.post.findMany({
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      }),

      // 今日统计
      Promise.all([
        // 今日新用户
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        // 今日新项目
        prisma.project.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        // 今日新申请
        prisma.application.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        })
      ]).then(([newUsers, newProjects, newApplications]) => ({
        newUsers,
        newProjects,
        newApplications
      }))
    ])

    const dashboardStats = {
      totalUsers,
      totalProjects,
      pendingApplications,
      totalTeamMembers,
      totalPosts,
      publishedPosts,
      recentProjects,
      recentApplications,
      recentPosts,
      todayStats,
      // 计算增长率（简化版本）
      growthRates: {
        users: todayStats.newUsers,
        projects: todayStats.newProjects,
        applications: todayStats.newApplications
      }
    }

    return NextResponse.json(dashboardStats)
  } catch (error) {
    console.error('Failed to fetch dashboard stats:', error)
    return NextResponse.json(
      { error: '获取仪表板数据失败' },
      { status: 500 }
    )
  }
})
