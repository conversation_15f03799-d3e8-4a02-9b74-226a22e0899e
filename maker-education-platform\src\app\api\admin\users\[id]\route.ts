import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        avatar: true,
        bio: true,
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户未找到' },
        { status: 404 }
      )
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Failed to fetch user:', error)
    return NextResponse.json(
      { error: '获取用户信息失败' },
      { status: 500 }
    )
  }
})

export const PATCH = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const { role, isActive, name, bio } = await request.json()

    // 防止管理员禁用自己
    if (params.id === session.user.id && isActive === false) {
      return NextResponse.json(
        { error: '不能禁用自己的账户' },
        { status: 400 }
      )
    }

    // 防止降级自己的权限
    if (params.id === session.user.id && role && role !== 'ADMIN') {
      return NextResponse.json(
        { error: '不能降级自己的权限' },
        { status: 400 }
      )
    }

    const updateData: any = {}
    if (role !== undefined) updateData.role = role
    if (isActive !== undefined) updateData.isActive = isActive
    if (name !== undefined) updateData.name = name
    if (bio !== undefined) updateData.bio = bio

    const user = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        avatar: true,
        bio: true,
      }
    })

    return NextResponse.json(user)
  } catch (error) {
    console.error('Failed to update user:', error)
    return NextResponse.json(
      { error: '更新用户信息失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    // 防止删除自己
    if (params.id === session.user.id) {
      return NextResponse.json(
        { error: '不能删除自己的账户' },
        { status: 400 }
      )
    }

    await prisma.user.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: '用户删除成功' })
  } catch (error) {
    console.error('Failed to delete user:', error)
    return NextResponse.json(
      { error: '删除用户失败' },
      { status: 500 }
    )
  }
})
