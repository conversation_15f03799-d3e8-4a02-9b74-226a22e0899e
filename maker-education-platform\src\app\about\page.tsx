'use client'

import { useState, useEffect } from 'react'
import { BookOpen, Users, Lightbulb, Award, Cpu, Printer, Wrench, Microscope, TrendingUp } from 'lucide-react'
import { useSettings } from '@/hooks/useSettings'

export default function About() {
  const { settings } = useSettings()
  const [mainEquipment, setMainEquipment] = useState<Array<{id: string, name: string, quantity: number, description: string}>>([])
  const [classroomSpecs, setClassroomSpecs] = useState<Array<{id: string, name: string, value: string, unit: string}>>([])

  useEffect(() => {
    // 解析主要设备数据
    if (settings.main_equipment) {
      try {
        const equipmentData = JSON.parse(settings.main_equipment)
        setMainEquipment(equipmentData)
      } catch (e) {
        setMainEquipment([])
      }
    }

    // 解析教室规格数据
    if (settings.classroom_specs) {
      try {
        const specsData = JSON.parse(settings.classroom_specs)
        setClassroomSpecs(specsData)
      } catch (e) {
        setClassroomSpecs([])
      }
    }
  }, [settings])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              关于我们的创客教室
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              专业的STEAM教育实验室，致力于培养学生的创新思维和实践能力
            </p>
          </div>
        </div>
      </section>

      {/* 教育理念 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              STEAM教育理念
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Science（科学）、Technology（技术）、Engineering（工程）、Arts（艺术）、Mathematics（数学）
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Microscope className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Science</h3>
              <p className="text-gray-600 text-sm">科学探索与实验</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Cpu className="h-10 w-10 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Technology</h3>
              <p className="text-gray-600 text-sm">技术应用与创新</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wrench className="h-10 w-10 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Engineering</h3>
              <p className="text-gray-600 text-sm">工程设计与制作</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-10 w-10 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Arts</h3>
              <p className="text-gray-600 text-sm">艺术创作与设计</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-10 w-10 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Mathematics</h3>
              <p className="text-gray-600 text-sm">数学建模与计算</p>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center text-white">
            <div className="animate-fade-in">
              <div className="text-4xl md:text-5xl font-bold mb-2">{settings.total_members || 50}+</div>
              <div className="text-blue-100">总成员数</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-4xl md:text-5xl font-bold mb-2">{settings.total_projects || 120}+</div>
              <div className="text-blue-100">总项目数</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-4xl md:text-5xl font-bold mb-2">{settings.active_members || 35}+</div>
              <div className="text-blue-100">活跃成员</div>
            </div>
            <div className="animate-fade-in">
              <div className="text-4xl md:text-5xl font-bold mb-2">{settings.completed_projects || 95}+</div>
              <div className="text-blue-100">完成项目</div>
            </div>
          </div>
        </div>
      </section>

      {/* 教室功能区域 */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              创客教室功能区域
            </h2>
            <p className="text-xl text-gray-600">
              专业设计的多功能学习空间，满足不同类型的创客活动需求
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Lightbulb className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">展示区</h3>
              <p className="text-gray-600">
                用于展示学生作品、项目成果和优秀案例，激发学习兴趣和创作灵感
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">讨论区</h3>
              <p className="text-gray-600">
                学生进行头脑风暴、团队协作和项目讨论的开放式学习空间
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <BookOpen className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">学习区</h3>
              <p className="text-gray-600">
                学生进行理论学习和课程学习的专门区域，配备现代化教学设备
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Wrench className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">制作区</h3>
              <p className="text-gray-600">
                小组制作和个人创作空间，提供各种工具和材料支持实践活动
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <Printer className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">设备区</h3>
              <p className="text-gray-600">
                配备3D打印机、激光切割机等先进设备，支持高精度制作需求
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                <Cpu className="h-6 w-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">编程区</h3>
              <p className="text-gray-600">
                专门的编程学习和机器人制作区域，培养学生的计算思维能力
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 设施设备 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              先进的设施设备
            </h2>
            <p className="text-xl text-gray-600">
              配备专业级设备，为学生提供最佳的学习和创作环境
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">主要设备清单</h3>
              <div className="space-y-4">
                {mainEquipment.length > 0 ? (
                  mainEquipment.map((equipment, index) => {
                    const colors = ['blue', 'green', 'purple', 'orange', 'red', 'indigo', 'pink', 'yellow']
                    const color = colors[index % colors.length]
                    return (
                      <div key={equipment.id} className="flex items-start">
                        <div className={`w-2 h-2 bg-${color}-600 rounded-full mt-2 mr-3`}></div>
                        <div>
                          <h4 className="font-semibold text-gray-900">
                            {equipment.name} {equipment.quantity > 1 && `(${equipment.quantity}台)`}
                          </h4>
                          <p className="text-gray-600 text-sm">{equipment.description}</p>
                        </div>
                      </div>
                    )
                  })
                ) : (
                  <>
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                      <div>
                        <h4 className="font-semibold text-gray-900">3D打印设备</h4>
                        <p className="text-gray-600 text-sm">高精度3D打印机，支持PLA、ABS等多种材料</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3"></div>
                      <div>
                        <h4 className="font-semibold text-gray-900">编程学习平台</h4>
                        <p className="text-gray-600 text-sm">图形化编程软件和硬件开发板</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 mr-3"></div>
                      <div>
                        <h4 className="font-semibold text-gray-900">机器人套件</h4>
                        <p className="text-gray-600 text-sm">多种类型的机器人制作和编程套件</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 mr-3"></div>
                      <div>
                        <h4 className="font-semibold text-gray-900">电子制作工具</h4>
                        <p className="text-gray-600 text-sm">电烙铁、万用表、示波器等电子制作工具</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-2 h-2 bg-red-600 rounded-full mt-2 mr-3"></div>
                      <div>
                        <h4 className="font-semibold text-gray-900">多媒体设备</h4>
                        <p className="text-gray-600 text-sm">投影仪、交互式白板、平板电脑等</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="bg-gray-100 p-8 rounded-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-4">教室规格</h3>
              <div className="space-y-3">
                {classroomSpecs.length > 0 ? (
                  classroomSpecs.map((spec) => (
                    <div key={spec.id} className="flex justify-between">
                      <span className="text-gray-600">{spec.name}：</span>
                      <span className="font-semibold">{spec.value}{spec.unit}</span>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">使用面积：</span>
                      <span className="font-semibold">80平方米以上</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">容纳人数：</span>
                      <span className="font-semibold">30人同时上课</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">工作台数：</span>
                      <span className="font-semibold">16个学生实验台</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">设备台数：</span>
                      <span className="font-semibold">16台3D打印机</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">展示空间：</span>
                      <span className="font-semibold">专门的作品展示区</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 课程体系 */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              创新课程体系
            </h2>
            <p className="text-xl text-gray-600">
              系统化的STEAM课程设计，培养学生的综合能力
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">基础课程</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 3D建模与设计</li>
                <li>• 图形化编程入门</li>
                <li>• 电子电路基础</li>
                <li>• 机械结构原理</li>
                <li>• 创意思维训练</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">进阶课程</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 机器人制作与编程</li>
                <li>• 智能硬件开发</li>
                <li>• 传感器应用</li>
                <li>• 物联网项目</li>
                <li>• 人工智能入门</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">项目课程</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 创新发明项目</li>
                <li>• 科技竞赛准备</li>
                <li>• 跨学科综合项目</li>
                <li>• 社会问题解决方案</li>
                <li>• 创业项目孵化</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
