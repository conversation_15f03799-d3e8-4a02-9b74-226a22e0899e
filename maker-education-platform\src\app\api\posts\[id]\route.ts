import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    if (!post) {
      return NextResponse.json(
        { error: '文章未找到' },
        { status: 404 }
      )
    }

    // 解析JSON字符串为数组
    const processedPost = {
      ...post,
      tags: post.tags ? JSON.parse(post.tags) : []
    }

    return NextResponse.json(processedPost)
  } catch (error) {
    console.error('Failed to fetch post:', error)
    return NextResponse.json(
      { error: '获取文章失败' },
      { status: 500 }
    )
  }
}

export const PUT = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const {
      title,
      content,
      excerpt,
      slug,
      published,
      featured,
      tags,
      metaTitle,
      metaDescription,
      metaKeywords,
      ogImage
    } = await request.json()

    // 验证必填字段
    if (!title || !content || !slug) {
      return NextResponse.json(
        { error: '标题、内容和链接为必填项' },
        { status: 400 }
      )
    }

    // 检查文章是否存在
    const existingPost = await prisma.post.findUnique({
      where: { id }
    })

    if (!existingPost) {
      return NextResponse.json(
        { error: '文章未找到' },
        { status: 404 }
      )
    }

    // 检查权限（只有作者或管理员可以编辑）
    if (existingPost.authorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无权限编辑此文章' },
        { status: 403 }
      )
    }

    // 检查slug是否与其他文章冲突
    if (slug !== existingPost.slug) {
      const slugConflict = await prisma.post.findUnique({
        where: { slug }
      })

      if (slugConflict) {
        return NextResponse.json(
          { error: '该链接已存在，请使用其他链接' },
          { status: 400 }
        )
      }
    }

    const updatedPost = await prisma.post.update({
      where: { id },
      data: {
        title,
        content,
        excerpt,
        slug,
        published,
        featured,
        tags: JSON.stringify(tags || []),
        metaTitle,
        metaDescription,
        metaKeywords,
        ogImage,
        updatedAt: new Date()
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedPost = {
      ...updatedPost,
      tags: updatedPost.tags ? JSON.parse(updatedPost.tags) : []
    }

    return NextResponse.json(processedPost)
  } catch (error) {
    console.error('Failed to update post:', error)
    return NextResponse.json(
      { error: '更新文章失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params

    // 检查文章是否存在
    const existingPost = await prisma.post.findUnique({
      where: { id }
    })

    if (!existingPost) {
      return NextResponse.json(
        { error: '文章未找到' },
        { status: 404 }
      )
    }

    // 检查权限（只有作者或管理员可以删除）
    if (existingPost.authorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无权限删除此文章' },
        { status: 403 }
      )
    }

    await prisma.post.delete({
      where: { id }
    })

    return NextResponse.json({ message: '文章删除成功' })
  } catch (error) {
    console.error('Failed to delete post:', error)
    return NextResponse.json(
      { error: '删除文章失败' },
      { status: 500 }
    )
  }
})

// 发布状态切换
export const PATCH = withAuth(async (
  request: NextRequest,
  session: any,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const { action, published, featured } = await request.json()

    // 检查文章是否存在
    const existingPost = await prisma.post.findUnique({
      where: { id }
    })

    if (!existingPost) {
      return NextResponse.json(
        { error: '文章未找到' },
        { status: 404 }
      )
    }

    // 检查权限
    if (existingPost.authorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无权限修改此文章' },
        { status: 403 }
      )
    }

    let updateData: any = {}

    if (action === 'toggle_publish') {
      updateData.published = published !== undefined ? published : !existingPost.published
    } else if (action === 'toggle_featured') {
      updateData.featured = featured !== undefined ? featured : !existingPost.featured
    } else {
      return NextResponse.json(
        { error: '无效的操作' },
        { status: 400 }
      )
    }

    const updatedPost = await prisma.post.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedPost = {
      ...updatedPost,
      tags: updatedPost.tags ? JSON.parse(updatedPost.tags) : []
    }

    return NextResponse.json(processedPost)
  } catch (error) {
    console.error('Failed to update post status:', error)
    return NextResponse.json(
      { error: '更新文章状态失败' },
      { status: 500 }
    )
  }
})
