import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAdmin } from '@/lib/auth-utils'

export const GET = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const post = await prisma.post.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    if (!post) {
      return NextResponse.json(
        { error: '文章未找到' },
        { status: 404 }
      )
    }

    // 解析JSON字符串为数组
    const processedPost = {
      ...post,
      tags: post.tags ? JSON.parse(post.tags) : []
    }

    return NextResponse.json(processedPost)
  } catch (error) {
    console.error('Failed to fetch post:', error)
    return NextResponse.json(
      { error: '获取文章失败' },
      { status: 500 }
    )
  }
})

export const PATCH = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    const { title, content, excerpt, published, featured, tags } = await request.json()

    const updateData: any = {}
    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (excerpt !== undefined) updateData.excerpt = excerpt
    if (published !== undefined) updateData.published = published
    if (featured !== undefined) updateData.featured = featured
    if (tags !== undefined) updateData.tags = JSON.stringify(tags)

    const post = await prisma.post.update({
      where: { id: params.id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    // 解析JSON字符串为数组
    const processedPost = {
      ...post,
      tags: post.tags ? JSON.parse(post.tags) : []
    }

    return NextResponse.json(processedPost)
  } catch (error) {
    console.error('Failed to update post:', error)
    return NextResponse.json(
      { error: '更新文章失败' },
      { status: 500 }
    )
  }
})

export const DELETE = withAdmin(async (
  request: NextRequest,
  session: any,
  { params }: { params: { id: string } }
) => {
  try {
    await prisma.post.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: '文章删除成功' })
  } catch (error) {
    console.error('Failed to delete post:', error)
    return NextResponse.json(
      { error: '删除文章失败' },
      { status: 500 }
    )
  }
})
