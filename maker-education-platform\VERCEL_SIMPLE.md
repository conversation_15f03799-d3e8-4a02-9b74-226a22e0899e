# Vercel + Supabase 免实名部署指南

## 🎯 最简单方案（无需实名认证）

### 第一步：注册账户（2分钟）
1. **Vercel**: https://vercel.com （邮箱注册，无需实名）
2. **Supabase**: https://supabase.com （邮箱注册，无需实名）

### 第二步：设置数据库（5分钟）
1. 访问 https://supabase.com
2. 点击 "Start your project"
3. 使用邮箱注册账户
4. 创建新项目：
   - 项目名称：`maker-education-db`
   - 密码：设置一个密码（记住它）
   - 地区：选择 Asia Pacific (Singapore)
5. 等待创建完成（1-2分钟）
6. 复制数据库连接字符串：
   - 点击左侧 "Settings" → "Database"
   - 复制 "URI" 格式的连接字符串

### 第三步：部署到Vercel（3分钟）
1. 访问 https://vercel.com
2. 使用邮箱注册账户
3. 点击 "New Project"
4. 选择 "Upload" 选项
5. 将整个 `maker-education-platform` 文件夹拖拽上传
6. 等待部署完成

### 第四步：设置环境变量（2分钟）
1. 在Vercel项目页面，点击 "Settings"
2. 点击 "Environment Variables"
3. 添加以下变量：

**DATABASE_URL**
```
您从Supabase复制的连接字符串
```

**NEXTAUTH_SECRET**
```
your-secret-key-32-chars-long-change-this
```

**NEXTAUTH_URL**
```
https://您的项目名.vercel.app
```

### 第五步：重新部署（1分钟）
1. 点击 "Deployments" 标签
2. 点击最新部署旁的三个点 → "Redeploy"

## 🎉 完成！

您的网站现在应该可以访问了！

### 测试网站
1. 访问您的Vercel域名
2. 测试用户注册
3. 管理员登录：用户名 `admin`，密码 `admin`

## 💰 费用
- **Vercel**: 完全免费，无限部署
- **Supabase**: 免费额度500MB，足够使用

## 🔧 如果遇到问题
- 检查环境变量是否正确设置
- 确认数据库连接字符串格式正确
- 重新部署一次

## 📞 需要帮助？
如果遇到问题，请告诉我具体的错误信息！ 