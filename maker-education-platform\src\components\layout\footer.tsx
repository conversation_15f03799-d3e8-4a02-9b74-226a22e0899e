import Link from 'next/link'
import { Mail, Phone, MapPin } from 'lucide-react'
import { useSettings } from '@/hooks/useSettings'

export default function Footer() {
  const { settings } = useSettings()

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 品牌信息 */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-blue-400 mb-4">{settings.site_name}</h3>
            <p className="text-gray-300 mb-4">
              {settings.site_description}
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-gray-300">
                <Mail className="w-4 h-4 mr-2" />
                <span>{settings.contact_email}</span>
              </div>
              <div className="flex items-center text-gray-300">
                <Phone className="w-4 h-4 mr-2" />
                <span>{settings.contact_phone}</span>
              </div>
              <div className="flex items-center text-gray-300">
                <MapPin className="w-4 h-4 mr-2" />
                <span>{settings.contact_address}</span>
              </div>
            </div>
          </div>

          {/* 快速链接 */}
          <div>
            <h4 className="text-lg font-semibold mb-4">快速链接</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-300 hover:text-blue-400 transition-colors">
                  关于我们
                </Link>
              </li>
              <li>
                <Link href="/projects" className="text-gray-300 hover:text-blue-400 transition-colors">
                  作品展示
                </Link>
              </li>
              <li>
                <Link href="/team" className="text-gray-300 hover:text-blue-400 transition-colors">
                  团队介绍
                </Link>
              </li>
              <li>
                <Link href="/apply" className="text-gray-300 hover:text-blue-400 transition-colors">
                  申请加入
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-blue-400 transition-colors">
                  博客文章
                </Link>
              </li>
            </ul>
          </div>

          {/* 教育资源 */}
          <div>
            <h4 className="text-lg font-semibold mb-4">教育资源</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/courses" className="text-gray-300 hover:text-blue-400 transition-colors">
                  STEAM课程
                </Link>
              </li>
              <li>
                <Link href="/projects?category=THREE_D_PRINTING" className="text-gray-300 hover:text-blue-400 transition-colors">
                  3D打印项目
                </Link>
              </li>
              <li>
                <Link href="/projects?category=ROBOTICS" className="text-gray-300 hover:text-blue-400 transition-colors">
                  机器人制作
                </Link>
              </li>
              <li>
                <Link href="/projects?category=PROGRAMMING" className="text-gray-300 hover:text-blue-400 transition-colors">
                  编程教学
                </Link>
              </li>
              <li>
                <Link href="/resources" className="text-gray-300 hover:text-blue-400 transition-colors">
                  学习资源
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-300 text-sm">
              © {settings.copyright_year} {settings.site_name}. 保留所有权利.
            </div>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                隐私政策
              </Link>
              <Link href="/terms" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                使用条款
              </Link>
              <Link href="/contact" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                联系我们
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
