'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft, Search, Lightbulb } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 404动画图标 */}
        <div className="relative mb-8">
          <div className="text-8xl md:text-9xl font-bold text-blue-100 select-none animate-pulse">
            404
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-24 h-24 md:w-32 md:h-32 bg-blue-600 rounded-full flex items-center justify-center animate-bounce">
              <Search className="w-12 h-12 md:w-16 md:h-16 text-white" />
            </div>
          </div>
        </div>

        {/* 标题和描述 */}
        <div className="mb-8 animate-fade-in">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            页面走丢了！
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            抱歉，您访问的页面不存在或已被移动
          </p>
          <p className="text-gray-500">
            让我们帮您找到正确的方向
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Link href="/">
            <Button size="lg" className="w-full sm:w-auto group hover:scale-105 transition-all duration-200">
              <Home className="w-5 h-5 mr-2 group-hover:animate-bounce" />
              返回首页
            </Button>
          </Link>
          <Button 
            variant="outline" 
            size="lg" 
            onClick={() => window.history.back()}
            className="w-full sm:w-auto group hover:scale-105 transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            返回上页
          </Button>
        </div>

        {/* 推荐链接 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 md:p-8 animate-slide-up">
          <div className="flex items-center justify-center mb-4">
            <Lightbulb className="w-6 h-6 text-yellow-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">
              您可能在寻找
            </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link 
              href="/about" 
              className="p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
            >
              <div className="font-medium text-gray-900 group-hover:text-blue-600">
                关于我们
              </div>
              <div className="text-sm text-gray-500">
                了解创客教育理念
              </div>
            </Link>
            
            <Link 
              href="/projects" 
              className="p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
            >
              <div className="font-medium text-gray-900 group-hover:text-blue-600">
                作品展示
              </div>
              <div className="text-sm text-gray-500">
                查看学生优秀作品
              </div>
            </Link>
            
            <Link 
              href="/team" 
              className="p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
            >
              <div className="font-medium text-gray-900 group-hover:text-blue-600">
                团队介绍
              </div>
              <div className="text-sm text-gray-500">
                认识我们的团队
              </div>
            </Link>
            
            <Link 
              href="/apply" 
              className="p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
            >
              <div className="font-medium text-gray-900 group-hover:text-blue-600">
                申请加入
              </div>
              <div className="text-sm text-gray-500">
                成为团队一员
              </div>
            </Link>
          </div>
        </div>

        {/* 装饰元素 */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-float"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 bg-indigo-200 rounded-full opacity-20 animate-float-delayed"></div>
        <div className="absolute top-1/2 left-5 w-12 h-12 bg-purple-200 rounded-full opacity-20 animate-float-slow"></div>
      </div>
    </div>
  )
}
