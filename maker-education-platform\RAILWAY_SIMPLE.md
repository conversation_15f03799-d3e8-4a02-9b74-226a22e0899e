# Railway + Supabase 免实名部署指南

## 🎯 全栈方案（无需实名认证）

### 第一步：注册账户（2分钟）
1. **Railway**: https://railway.app （邮箱注册，无需实名）
2. **Supabase**: https://supabase.com （邮箱注册，无需实名）

### 第二步：设置数据库（5分钟）
1. 访问 https://supabase.com
2. 点击 "Start your project"
3. 使用邮箱注册账户
4. 创建新项目：
   - 项目名称：`maker-education-db`
   - 密码：设置一个密码（记住它）
   - 地区：选择 Asia Pacific (Singapore)
5. 等待创建完成（1-2分钟）
6. 复制数据库连接字符串：
   - 点击左侧 "Settings" → "Database"
   - 复制 "URI" 格式的连接字符串

### 第三步：部署到Railway（5分钟）

#### 方法一：使用CLI（推荐）
1. 安装Railway CLI：
   ```bash
   npm install -g @railway/cli
   ```

2. 登录Railway：
   ```bash
   railway login
   ```

3. 在项目目录中初始化：
   ```bash
   cd maker-education-platform
   railway init
   ```

4. 部署项目：
   ```bash
   railway up
   ```

#### 方法二：使用网页界面
1. 访问 https://railway.app
2. 注册账户（使用邮箱）
3. 点击 "Start a New Project"
4. 选择 "Deploy from GitHub" 或 "Deploy from Template"
5. 如果选择模板，选择 "Node.js" 模板

### 第四步：配置环境变量（2分钟）
在Railway项目设置中，添加以下环境变量：

**DATABASE_URL**
```
您从Supabase复制的数据库连接字符串
```

**NEXTAUTH_SECRET**
```
your-secret-key-32-chars-long-change-this
```

**NEXTAUTH_URL**
```
https://您的项目名.railway.app
```

### 第五步：重新部署（1分钟）
1. 部署完成后，Railway会显示您的网站URL
2. 回到Railway项目设置
3. 点击 "Variables" 标签
4. 编辑 NEXTAUTH_URL，改为您的实际域名
5. 重新部署：点击 "Deployments" → 最新部署旁的三个点 → "Redeploy"

## 🎉 完成！

您的网站现在应该可以访问了！

### 测试网站
1. 访问您的Railway域名
2. 测试用户注册
3. 管理员登录：用户名 `admin`，密码 `admin`

## 💰 费用
- **Railway**: 免费额度500小时/月
- **Supabase**: 免费额度500MB，足够使用

## 🔧 如果遇到问题
- 检查环境变量是否正确设置
- 确认数据库连接字符串格式正确
- 查看Railway部署日志

## 📞 需要帮助？
如果遇到问题，请告诉我具体的错误信息！ 